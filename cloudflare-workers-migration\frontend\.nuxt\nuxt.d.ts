// Generated by nuxi
/// <reference types="@vueuse/nuxt" />
/// <reference types="@nuxt/image" />
/// <reference types="@nuxt/ui" />
/// <reference types="@nuxt/devtools" />
/// <reference types="@pinia/nuxt" />
/// <reference types="nuxt-gtag" />
/// <reference types="@nuxt/telemetry" />
/// <reference types="@nuxtjs/i18n" />
/// <reference path="types/builder-env.d.ts" />
/// <reference types="nuxt" />
/// <reference path="types/app-defaults.d.ts" />
/// <reference path="types/plugins.d.ts" />
/// <reference path="types/build.d.ts" />
/// <reference path="types/schema.d.ts" />
/// <reference path="types/app.config.d.ts" />
/// <reference types="@pinia/nuxt" />
/// <reference path="ui.colors.d.ts" />
/// <reference path="types/i18n-plugin.d.ts" />
/// <reference types="vue-router" />
/// <reference path="types/middleware.d.ts" />
/// <reference path="types/nitro-middleware.d.ts" />
/// <reference path="types/layouts.d.ts" />
/// <reference path="components.d.ts" />
/// <reference path="imports.d.ts" />
/// <reference path="types/imports.d.ts" />
/// <reference path="schema/nuxt.schema.d.ts" />
/// <reference path="types/tailwind.config.d.ts" />
/// <reference path="types/nitro.d.ts" />

export {}
