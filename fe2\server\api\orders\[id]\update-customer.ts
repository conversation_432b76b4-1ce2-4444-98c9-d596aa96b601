import { define<PERSON><PERSON><PERSON><PERSON><PERSON>, readBody, getRequestUR<PERSON>, createError } from 'h3'
import { useRuntimeConfig } from '#imports'
import { getSession } from '~/server/utils/auth'
import { AUTH_COOKIE_NAME } from '~/constants/auth'

export default defineEventHandler(async (event) => {
  try {
    // NOTE: This endpoint still uses the old Medusa admin API directly
    // It may need to be updated to work with the Cloudflare backend or removed if no longer needed
    
    // Get Medusa API URL and admin token from config
    const config = useRuntimeConfig()
    const medusaUrl = config.medusaUrl || 'https://api.handmadein.ro'
    const adminToken = config.medusaAdminToken
    
    if (!adminToken) {
      console.error('Admin token not found in configuration')
      throw createError({
        statusCode: 500,
        message: 'Server configuration error'
      })
    }
    
    // Get order ID from URL
    const url = getRequestURL(event)
    const id = event.context.params?.id
    
    if (!id) {
      throw createError({
        statusCode: 400,
        message: 'Order ID is required'
      })
    }
    
    console.log(`Processing order update for order ID: ${id}`)
    
    // Get customer ID from request body
    const body = await readBody(event)
    const customerId = body.customerId
    
    if (!customerId) {
      throw createError({
        statusCode: 400,
        message: 'Customer ID is required'
      })
    }
    
    console.log(`Updating order ${id} to associate with customer ${customerId}`)
    
    // Get authentication state to verify the request
    const session = await getSession(event)
    
    // Verify authentication - check if user is authenticated
    if (!session || !session.isAuthenticated || !session.email) {
      throw createError({
        statusCode: 401,
        message: 'Authentication required'
      })
    }
    
    // Update the order using Admin API
    const updateResponse = await fetch(`${medusaUrl}/admin/orders/${id}`, {
      method: 'POST',
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${adminToken}`
      },
      body: JSON.stringify({
        customer_id: customerId
      })
    })
    
    if (!updateResponse.ok) {
      const errorData = await updateResponse.json().catch(() => ({}))
      console.error('Error updating order customer association:', errorData)
      
      throw createError({
        statusCode: updateResponse.status,
        message: `Failed to update order: ${errorData.message || updateResponse.statusText}`
      })
    }
    
    const updateData = await updateResponse.json()
    console.log(`Successfully updated order ${id} with customer ${customerId}`)
    
    return {
      success: true,
      order: updateData.order
    }
  } catch (error: any) {
    console.error('Error in update-customer endpoint:', error.message)
    throw createError({
      statusCode: error.statusCode || 500,
      message: error.message || 'An unknown error occurred'
    })
  }
}) 