import { defineEvent<PERSON><PERSON><PERSON>, readBody, createError } from 'h3'

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const { email, password } = body

    if (!email || !password) {
      throw createError({
        statusCode: 400,
        message: 'Email and password are required'
      })
    }
    
    // Forward the request to the Cloudflare backend
    const cloudflareApiUrl = 'http://localhost:8787'
    
    console.log(`Using Cloudflare backend for authentication: ${cloudflareApiUrl}`)
    
    const response = await fetch(`${cloudflareApiUrl}/auth/customer/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept-Language': 'ro',
        'x-locale': 'ro'
      },
      body: JSON.stringify({
        email,
        password
      })
    })
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw createError({
        statusCode: response.status,
        message: errorData.message || errorData.error || 'Authentication failed'
      })
    }
    
    const responseData = await response.json()
    
    // Return the response in the format expected by the frontend
    if (responseData.success && responseData.data) {
      return {
        success: true,
        data: responseData.data,
        message: responseData.message || 'Login successful'
      }
    } else {
      throw createError({
        statusCode: 400,
        message: responseData.error || responseData.message || 'Authentication failed'
      })
    }
  } catch (error: any) {
    console.error('Login error:', error)
    
    // If it's already a createError, re-throw it
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      message: error.message || 'Authentication failed'
    })
  }
}) 