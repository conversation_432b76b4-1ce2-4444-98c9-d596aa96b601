import { define<PERSON><PERSON><PERSON><PERSON><PERSON>, readBody, createError } from 'h3'
import { getSession } from '~/server/utils/auth'

export default defineEventHandler(async (event) => {
  // Check if user is authenticated
  const session = await getSession(event)
  if (!session || !session.isAuthenticated) {
    throw createError({
      statusCode: 401,
      message: 'Unauthorized'
    })
  }

  // Only allow POST requests for password changes
  const method = event.node.req.method
  if (method !== 'POST') {
    throw createError({
      statusCode: 405,
      message: 'Method not allowed'
    })
  }

  try {
    // Get the password data
    const { current_password, new_password } = await readBody(event)
    
    if (!current_password || !new_password) {
      throw createError({
        statusCode: 400,
        message: 'Both current and new password are required'
      })
    }

    // Forward the request to the Cloudflare backend
    const cloudflareApiUrl = 'http://localhost:8787'
    const response = await fetch(`${cloudflareApiUrl}/store/customers/password`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Accept-Language': 'ro',
        'x-locale': 'ro',
        'Authorization': `Bearer ${session.accessToken}`
      },
      body: JSON.stringify({
        current_password,
        new_password
      })
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: 'Request failed' }))
      throw createError({
        statusCode: response.status,
        message: errorData.error || errorData.message || 'Failed to update password'
      })
    }

    const data = await response.json()
    
    return { 
      success: true, 
      message: data.message || 'Password updated successfully'
    }
  } catch (error: any) {
    // If it's already a createError, re-throw it
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      message: error.message || 'Failed to update password'
    })
  }
}) 