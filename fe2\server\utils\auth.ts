import { getCookie } from 'h3'
import type { H3Event } from 'h3'
import { AUTH_COOKIE_NAME } from '~/constants/auth'

export interface AuthSession {
  isAuthenticated: boolean
  userId?: string
  username?: string
  email?: string
  name?: string
  given_name?: string
  family_name?: string
  phone_number?: string
  accessToken?: string
  customer?: {
    id: string
    first_name: string
    last_name: string
    email: string
    phone?: string
  }
}

/**
 * Get session information from the request cookie
 */
export async function getSession(event: H3Event): Promise<AuthSession | null> {
  try {
    // Get the auth cookie
    const authCookie = getCookie(event, AUTH_COOKIE_NAME)
    
    if (!authCookie) {
      return null
    }
    
    // Try to parse the cookie data
    try {
      const cookieData = JSON.parse(authCookie)
      
      if (cookieData && cookieData.isAuthenticated) {
        return {
          isAuthenticated: true,
          userId: cookieData.userId,
          username: cookieData.username || cookieData.email,
          email: cookieData.email,
          name: cookieData.name,
          given_name: cookieData.given_name,
          family_name: cookieData.family_name,
          phone_number: cookieData.phone_number,
          accessToken: cookieData.accessToken,
          customer: cookieData.customer
        }
      }
    } catch (e) {
      console.error('Error parsing auth cookie:', e)
    }
    
    return null
  } catch (error) {
    console.error('Error getting session:', error)
    return null
  }
}

/**
 * Get customer ID from authentication session
 */
export async function getCustomerIdFromAuth(event: H3Event): Promise<string | null> {
  const session = await getSession(event)
  
  if (!session || !session.isAuthenticated) {
    return null
  }
  
  // Try to get customer ID from the customer object first
  if (session.customer?.id) {
    return session.customer.id
  }
  
  // Fallback to userId if available
  return session.userId || null
}

/**
 * Deprecated: Old Cognito sync functions - no longer needed with Cloudflare backend
 */
export async function syncCognitoToMedusa(cognitoUser: any): Promise<any> {
  console.warn('syncCognitoToMedusa is deprecated - using new Cloudflare backend authentication')
  return null
}

/**
 * Deprecated: Old Cognito sync functions - no longer needed with Cloudflare backend
 */
export async function syncCognitoToMedusaDirectly(cognitoUser: any): Promise<any> {
  console.warn('syncCognitoToMedusaDirectly is deprecated - using new Cloudflare backend authentication')
  return null
} 