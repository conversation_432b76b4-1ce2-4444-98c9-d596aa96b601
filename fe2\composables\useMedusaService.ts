import { ref, computed } from 'vue'
import { useLocaleStore } from '~/stores/locale'
import { useCookie } from 'nuxt/app'
import { useCustomer } from '~/composables/useCustomer'
import { useToast } from '#imports'
import { useRuntimeConfig } from '#app'
import { AUTH_COOKIE_NAME } from '~/constants/auth'

// Cloudflare API fetch function
const cloudflareApiFetch = async (endpoint: string, options: RequestInit = {}) => {
  const headers: any = {
    'Content-Type': 'application/json',
    'Accept-Language': 'ro',
    'x-locale': 'ro',
    ...options.headers,
  }
  
  // Add authentication token if available
  if (process.client) {
    // Try multiple methods to get the auth token
    let token = null;
    
    // Method 1: Direct cookie access (like AddressManagement component)
    if (typeof document !== 'undefined') {
      const cookies = document.cookie.split(';');
      const authCookie = cookies.find(cookie => cookie.trim().startsWith(`${AUTH_COOKIE_NAME}=`));
      if (authCookie) {
        try {
          const authData = JSON.parse(decodeURIComponent(authCookie.split('=')[1]));
          token = authData.accessToken;
        } catch (e) {
          console.warn('Error parsing auth cookie in cloudflareApiFetch:', e);
        }
      }
    }
    
    // Method 2: Check localStorage as fallback
    if (!token) {
      token = localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
    }
    
    // Method 3: Check useCookie as fallback
    if (!token) {
      try {
        const authCookie = useCookie(AUTH_COOKIE_NAME);
        if (authCookie.value) {
          const authData = typeof authCookie.value === 'string' 
            ? JSON.parse(authCookie.value) 
            : authCookie.value;
          token = authData?.accessToken;
        }
      } catch (e) {
        console.warn('Error getting auth from useCookie:', e);
      }
    }
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
  }
  
  const baseUrl = 'http://localhost:8787'
  const url = endpoint.startsWith('http') ? endpoint : `${baseUrl}${endpoint}`
  
  try {
    const response = await fetch(url, {
      ...options,
      headers,
    })
    
    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'An unknown error occurred' }))
      console.error(`API Error (${response.status}):`, error)
      throw new Error(error.message || 'Request failed')
    }
    
    const data = await response.json()
    return data
  } catch (error) {
    console.error('API fetch error:', error)
    throw error
  }
}

// Helper function to get auth token
const getToken = (): string | null => {
  if (process.client) {
    const token = localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token')
    return token
  }
  return null
}

export const useMedusaService = () => {
  const toast = useToast()
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const localeStore = useLocaleStore()
  
  // Set a cookie for cart ID with expiry of 90 days
  const setCartCookie = (cartId: string) => {
    const currentCartId = getCartCookie();
    
    if (currentCartId !== cartId) {
      console.log(`Setting cart cookie: ${cartId} (was: ${currentCartId || 'none'})`);
      const cartCookie = useCookie('cart-id', {
        maxAge: 60 * 60 * 24 * 90, // 90 days
        path: '/',
        sameSite: 'lax'
      });
      cartCookie.value = cartId;
    }
  }
  
  // Get cart ID from cookie
  const getCartCookie = (): string | null => {
    const cartCookie = useCookie('cart-id')
    return cartCookie.value || null
  }
  
  const getCartId = (): string | null => {
    return getCartCookie()
  }
  
  // Cart functions
  const retrieveCart = async (cartId: string, forceRefresh: boolean = false) => {
    if (!cartId) {
      console.error('Cart ID is required for retrieveCart')
      return null
    }
    
    isLoading.value = true
    error.value = null
    
    try {
      // Use cloudflareApiFetch instead of standard fetch
      const response = await cloudflareApiFetch(`/store/carts/${cartId}`)
      
      // Check if response has the correct structure { success: true, data: cart }
      if (!response.success) {
        if (response.error === 'Cart not found') {
          console.log('Cart not found, will need to create a new one')
          return null
        }
        throw new Error(response.error || 'Failed to retrieve cart')
      }
      
      return response.data
    } catch (err: any) {
      console.error('Error retrieving cart:', err)
      error.value = err.message || 'Failed to retrieve cart'
      return null
    } finally {
      isLoading.value = false
    }
  }
  
  const createCart = async (regionId?: string) => {
    isLoading.value = true
    error.value = null
    
    try {
      const localeStore = useLocaleStore()
      
      let effectiveRegionId = regionId
      
      if (!effectiveRegionId) {
        effectiveRegionId = localeStore.currentCountry?.regionId
        
        // If still no region ID, try to get regions
        if (!effectiveRegionId) {
          const regions = await getRegions()
          if (regions && regions.length > 0) {
            effectiveRegionId = regions[0].id
          }
        }
      }
      
      if (!effectiveRegionId) {
        throw new Error('No region ID available to create cart')
      }
      
      // Check if user is authenticated and get customer ID if available
      let customerId = null
      if (process.client) {
        try {
          // Try to get auth data from cookie using useCookie
          const authCookie = useCookie(AUTH_COOKIE_NAME)
          
          if (authCookie.value) {
            let authData
            
            // Handle string vs object value
            if (typeof authCookie.value === 'string') {
              try {
                authData = JSON.parse(authCookie.value)
              } catch (e) {
                console.warn('Failed to parse auth cookie:', e)
              }
            } else {
              // Already an object
              authData = authCookie.value
            }
            
            if (authData && authData.isAuthenticated && authData.customer?.id) {
              customerId = authData.customer.id
              console.log('Found authenticated customer ID for cart creation:', customerId)
            }
          }
        } catch (e) {
          console.warn('Error checking authentication status during cart creation:', e)
        }
      }
      
      // Create the request body
      const requestBody: Record<string, any> = {
        region_id: effectiveRegionId,
      }
      
      // Get the locale-language and locale-country cookies
      const languageCookie = useCookie('locale-language').value
      const countryCookie = useCookie('locale-country').value
      
      // Add metadata
      requestBody.metadata = {}
      if (languageCookie) {
        requestBody.metadata.cookie_locale = languageCookie
      }
      if (countryCookie) {
        requestBody.metadata['locale-country'] = countryCookie
      }
      console.log(`Adding metadata to cart during creation: ${JSON.stringify(requestBody.metadata)}`)
      
      if (customerId) {
        requestBody.customer_id = customerId 
      }
      
      // Use cloudflareApiFetch instead of standard fetch
      const response = await cloudflareApiFetch('/store/carts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      })
      
      // Check if response has the correct structure { success: true, data: cart }
      if (!response.success || !response.data) {
        throw new Error('Invalid response from cart creation')
      }
      
      const cart = response.data
      
      // Store cart ID in cookie for persistence
      if (cart && cart.id) {
        setCartCookie(cart.id)
      }
      
      toast.add({
        title: 'Success',
        description: 'Cart created successfully',
        color: 'green'
      })
      return cart
    } catch (err: any) {
      error.value = err.message || 'Failed to create cart'
      toast.add({
        title: 'Error',
        description: err.message || 'Failed to create cart',
        color: 'red'
      })
      return null
    } finally {
      isLoading.value = false
    }
  }
  
  const addToCart = async (cartId: string, variantId: string, quantity: number) => {
    isLoading.value = true
    error.value = null
    
    try {
      // Always try to get the most current cart ID from localStorage first
      let effectiveCartId = localStorage.getItem('cart-id')
      
      // If not available in localStorage, use the provided cartId or cookie as fallback
      if (!effectiveCartId) {
        effectiveCartId = cartId || getCartCookie()
      }

      if (!effectiveCartId) {
        throw new Error('No cart ID available')
      }

      console.log(`Using cart ID for adding item: ${effectiveCartId}`)

      // Use cloudflareApiFetch instead of manual URL construction
      const data = await cloudflareApiFetch(`/store/carts/${effectiveCartId}/line-items`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          variant_id: variantId,
          quantity: quantity
        })
      })

      // Update cookie with the cart ID
      setCartCookie(effectiveCartId)

      return data.data || data.cart
    } catch (err: any) {
      error.value = err.message || 'Failed to add item to cart'
      toast.add({
        title: 'Error',
        description: 'Failed to add item to cart',
        color: 'red'
      })
      return null
    } finally {
      isLoading.value = false
    }
  }
  
  const updateLineItem = async (cartId: string, lineId: string, quantity: number) => {
    isLoading.value = true
    error.value = null
    
    try {
      // Always try to get the most current cart ID from localStorage first
      let effectiveCartId = localStorage.getItem('cart-id')
      
      // If not available in localStorage, use the provided cartId or cookie as fallback
      if (!effectiveCartId) {
        effectiveCartId = cartId || getCartCookie()
      }

      if (!effectiveCartId) {
        throw new Error('No cart ID available')
      }

      console.log(`Using cart ID for line item update: ${effectiveCartId}`)

      // Use cloudflareApiFetch instead of manual URL construction
      const data = await cloudflareApiFetch(`/store/carts/${effectiveCartId}/line-items/${lineId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          quantity
        })
      })

      // Update cookie with the cart ID
      setCartCookie(effectiveCartId)

      return data.data || data.cart
    } catch (err: any) {
      error.value = err.message || 'Failed to update item'
      toast.add({
        title: 'Error',
        description: 'Failed to update item',
        color: 'red'
      })
      return null
    } finally {
      isLoading.value = false
    }
  }
  
  const removeLineItem = async (cartId: string, lineId: string) => {
    isLoading.value = true
    error.value = null
    
    try {
      // Always try to get the most current cart ID from localStorage first
      let effectiveCartId = localStorage.getItem('cart-id')
      
      // If not available in localStorage, use the provided cartId or cookie as fallback
      if (!effectiveCartId) {
        effectiveCartId = cartId || getCartCookie()
      }

      if (!effectiveCartId) {
        throw new Error('No cart ID available')
      }

      console.log(`Using cart ID for line item removal: ${effectiveCartId}`)

      // Use cloudflareApiFetch instead of manual URL construction
      const data = await cloudflareApiFetch(`/store/carts/${effectiveCartId}/line-items/${lineId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      // Update cookie with the cart ID
      setCartCookie(effectiveCartId)

      // Check if we received a deletion confirmation or a cart
      if (data && data.deleted === true) {
        // This is a deletion confirmation, not a cart
        toast.add({
          title: 'Success',
          description: 'Item removed from cart',
          color: 'green'
        })
        return data
      } else if (data && (data.data || data.cart)) {
        // This is a cart response
        toast.add({
          title: 'Success',
          description: 'Item removed from cart',
          color: 'green'
        })
        return data.data || data.cart
      } else {
        console.warn('Unexpected response format from remove line item:', data)
        // If no cart in response, return the data as-is
        return data
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to remove item'
      toast.add({
        title: 'Error',
        description: 'Failed to remove item',
        color: 'red'
      })
      return null
    } finally {
      isLoading.value = false
    }
  }
  
  const updateCart = async (cartId: string, data: any) => {
    isLoading.value = true
    error.value = null
    
    try {
      // Always try to get the most current cart ID from localStorage first
      let effectiveCartId = localStorage.getItem('cart-id')
      
      // If not available in localStorage, use the provided cartId or cookie as fallback
      if (!effectiveCartId) {
        effectiveCartId = cartId || getCartCookie()
      }
      
      if (!effectiveCartId) {
        throw new Error('No cart ID available')
      }
      
      console.log(`Using cart ID for cart update: ${effectiveCartId}`)
      
      // Get the locale-language cookie and add it to metadata
      const languageCookie = useCookie('locale-language').value
      const countryCookie = useCookie('locale-country').value
      
      // Ensure metadata object exists
      if (!data.metadata) {
        data.metadata = {}
      }
      
      // Add language and country from cookies to metadata
      if (languageCookie) {
        data.metadata.cookie_locale = languageCookie
      }
      if (countryCookie) {
        data.metadata['locale-country'] = countryCookie
      }
      console.log(`Adding metadata to cart during update: ${JSON.stringify(data.metadata)}`)
      
      // Use cloudflareApiFetch instead of direct Medusa API
      const response = await cloudflareApiFetch(`/store/carts/${effectiveCartId}`, {
        method: 'POST',
        body: JSON.stringify(data)
      })
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to update cart')
      }
      
      // Update cookie with the cart ID
      setCartCookie(effectiveCartId)
      
      return response.data
    } catch (err: any) {
      error.value = err.message || 'Failed to update cart'
      toast.add({
        title: 'Error',
        description: 'Failed to update cart',
        color: 'red'
      })
      return null
    } finally {
      isLoading.value = false
    }
  }
  
  // Product functions - removed unused loadBestSellers() and getProducts()
  
  const getProduct = async (handle: string) => {
    isLoading.value = true
    error.value = null
    
    try {
      const localeStore = useLocaleStore()
      
      // Get region_id and currency from locale store
      const regionId = localeStore.currentCountry?.regionId
      const currencyCode = localeStore.currentCountry?.currencies[0] || 'RON'
      
      const queryParams = new URLSearchParams()
      if (regionId) queryParams.append('region_id', regionId)
      if (currencyCode) queryParams.append('currency_code', currencyCode)
      
      // Use cloudflareApiFetch for Cloudflare backend
      const response = await cloudflareApiFetch(`/store/products/${handle}?${queryParams.toString()}`)
      
      // Check if response has the correct structure { success: true, data: product }
      if (!response.success || !response.data) {
        throw new Error(response.error || 'Product not found')
      }
      
      const productData = response.data
      
      // Process variants to include product_title if available
      if (productData && productData.variants) {
        productData.variants = productData.variants.map((variant: any) => ({
          ...variant,
          product_title: productData.title
        }))
      }
      
      return productData
    } catch (err: any) {
      error.value = err.message || 'Failed to get product'
      return null
    } finally {
      isLoading.value = false
    }
  }
  
  // New method to get a product variant by ID
  const getVariant = async (variantId: string) => {
    isLoading.value = true
    error.value = null
    
    try {
      const localeStore = useLocaleStore()
      
      // Get region_id and currency from locale store
      const regionId = localeStore.currentCountry?.regionId
      const currencyCode = localeStore.currentCountry?.currencies?.[0] || 'RON'
      
      const queryParams = new URLSearchParams()
      if (regionId) queryParams.append('region_id', regionId)
      if (currencyCode) queryParams.append('currency_code', currencyCode)
      
      // Use cloudflareApiFetch for Cloudflare backend
      const response = await cloudflareApiFetch(`/store/products/variants/${variantId}?${queryParams.toString()}`)
      
      // Check if response has the correct structure { success: true, data: variant }
      if (!response.success || !response.data) {
        throw new Error(response.error || 'Variant not found')
      }
      
      const variantData = response.data
      
      // Add product_title to the variant if product data is available
      if (variantData && variantData.product && variantData.product.title) {
        variantData.product_title = variantData.product.title
      }
      
      return variantData
    } catch (err: any) {
      error.value = err.message || 'Failed to get variant'
      console.error('Error fetching variant:', err)
      return null
    } finally {
      isLoading.value = false
    }
  }
  
  // New method to get multiple variants by IDs
  const getBatchVariants = async (variantIds: string[]) => {
    isLoading.value = true
    error.value = null
    
    try {
      if (!variantIds || variantIds.length === 0) {
        return []
      }
      
      // Use Cloudflare API
      const localeStore = useLocaleStore()

      // Get region_id from locale store
      const regionId = localeStore.currentCountry?.regionId

      // Get currency code from locale store
      const currencyCode = localeStore.currentCountry?.currencies?.[0] || 'RON'

      // Join IDs with commas for the query parameter
      const idsParam = variantIds.join(',')

      // Build query parameters
      const queryParams = new URLSearchParams()
      queryParams.append('ids', idsParam)
      queryParams.append('currency_code', currencyCode)
      if (regionId) {
        queryParams.append('region_id', regionId)
      }

      console.log(`Fetching batch variants with params: ${queryParams.toString()}`)

      const response = await cloudflareApiFetch(`/store/variants/batch?${queryParams.toString()}`)

      if (!response.success) {
        throw new Error(response.error || 'Failed to get batch variants')
      }

      const data = response.data || []

      if (!Array.isArray(data)) {
        return []
      }

      // Ensure each variant has product_title if product data is available
      const enhancedVariants = data.map((variant: any) => {
        if (variant.product && variant.product.title) {
          return {
            ...variant,
            product_title: variant.product.title
          }
        }
        return variant
      })

      return enhancedVariants
    } catch (err: any) {
      error.value = err.message || 'Failed to get batch variants'
      console.error('Error fetching batch variants:', err)
      return []
    } finally {
      isLoading.value = false
    }
  }
  
  // Collection functions
  const getCollections = async () => {
    isLoading.value = true
    error.value = null
    
    try {
      // Use cloudflareApiFetch for Cloudflare backend
      const response = await cloudflareApiFetch('/store/collections')
      
      // Check if response has the correct structure { success: true, data: collections }
      if (!response.success || !response.data) {
        throw new Error(response.error || 'Failed to fetch collections')
      }
      
      return response.data
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch collections'
      console.error('Error fetching collections:', err)
      return []
    } finally {
      isLoading.value = false
    }
  }
  
  const getCollection = async (handle: string) => {
    isLoading.value = true
    error.value = null
    
    try {
      // Use cloudflareApiFetch for Cloudflare backend
      const response = await cloudflareApiFetch(`/store/collections/${handle}`)
      
      // Check if response has the correct structure { success: true, data: collection }
      if (!response.success || !response.data) {
        throw new Error(response.error || 'Collection not found')
      }
      
      return response.data
    } catch (err: any) {
      error.value = err.message || 'Failed to get collection'
      console.error('Error fetching collection:', err)
      return null
    } finally {
      isLoading.value = false
    }
  }
  
  // Region and country functions
  const getRegions = async () => {
    isLoading.value = true
    error.value = null
    
    try {
      // Use cloudflareApiFetch for Cloudflare backend
      const response = await cloudflareApiFetch('/store/regions')
      
      // Check if response has the correct structure { success: true, data: regions }
      if (!response.success || !response.data) {
        throw new Error(response.error || 'Failed to fetch regions')
      }
      
      toast.add({
        title: 'Success',
        description: 'Regions loaded successfully',
        color: 'green'
      })
      return response.data
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch regions'
      toast.add({
        title: 'Error',
        description: err.message || 'Failed to fetch regions',
        color: 'red'
      })
      return null
    } finally {
      isLoading.value = false
    }
  }
  
  // Customer functions - removed unused createCustomer and loginCustomer

  const getCustomer = async () => {
    isLoading.value = true
    error.value = null

    try {
      // First check if user is authenticated via useAuth
      const { isAuthenticated, user } = useAuth()

      if (!isAuthenticated.value) {
        console.log('User is not authenticated, skipping customer fetch')
        return null
      }

      // Try to get customer from store first
      const { customer, fetchCustomer } = useCustomer()

      if (customer.value && customer.value.email) {
        console.log('Retrieved customer from store:', customer.value)
        return customer.value
      }

      // If customer store is empty but user is authenticated, fetch customer data
      console.log('Customer store is empty but user is authenticated, fetching customer data...')

      try {
        const fetchedCustomer = await fetchCustomer()
        if (fetchedCustomer && fetchedCustomer.email) {
          console.log('Successfully fetched customer from API:', fetchedCustomer)
          return fetchedCustomer
        }
      } catch (fetchError) {
        console.log('Error fetching customer via useCustomer, trying direct API call:', fetchError)
      }

      // Fallback: Use direct cloudflareApiFetch with the working /store/customers/profile endpoint
      const response = await cloudflareApiFetch('/store/customers/profile')

      if (response && response.success && response.data) {
        console.log('Retrieved customer from direct profile API call:', response.data)
        return response.data
      }

      // If no success response, the user is likely not authenticated
      return null
    } catch (err: any) {
      console.log('Error getting customer (likely not authenticated):', err.message)
      // Don't set error.value for authentication issues as this is expected for non-authenticated users
      if (err.message && !err.message.includes('authentication') && !err.message.includes('401')) {
        error.value = err.message || 'Failed to get customer'
      }
      return null
    } finally {
      isLoading.value = false
    }
  }

  // Customer Address Methods
  const getCustomerAddresses = async () => {
    isLoading.value = true
    error.value = null

    try {
      // Use the Cloudflare backend for customer addresses
      const response = await cloudflareApiFetch('/store/customers/me/addresses')

      if (response && response.success) {
        return { addresses: response.data || [] }
      }

      throw new Error('Failed to get addresses')
    } catch (err: any) {
      error.value = err.message || 'Failed to get addresses'
      console.error('Failed to get addresses:', err)
      return { addresses: [] }
    } finally {
      isLoading.value = false
    }
  }
  
  // Order functions
  const retrieveOrder = async (orderId: string) => {
    isLoading.value = true
    error.value = null

    try {
      // Use cloudflareApiFetch for consistency
      const response = await cloudflareApiFetch(`/store/orders/${orderId}?fields=*&expand=customer,items,shipping_address,billing_address,discounts,shipping_methods,payments`)

      if (!response.success) {
        throw new Error(`Failed to retrieve order: ${response.error}`)
      }

      return response.data
    } catch (err: any) {
      error.value = err.message || 'Failed to retrieve order'
      console.error('Error retrieving order:', err)
      return null
    } finally {
      isLoading.value = false
    }
  }

  // Removed unused order functions: completeCart, getOrders
  
  // Checkout methods
  const addShippingAddress = async (cartId: string, address: any) => {
    isLoading.value = true
    error.value = null
    
    try {
      // If cartId wasn't provided, try to get it from cookie
      let effectiveCartId = cartId
      if (!effectiveCartId) {
        const cookieCartId = getCartCookie()
        if (!cookieCartId) {
          throw new Error('No cart ID available')
        }
        effectiveCartId = cookieCartId
      }
      
      // Use cloudflareApiFetch instead of direct Medusa API
      const response = await cloudflareApiFetch(`/store/carts/${effectiveCartId}`, {
        method: 'POST',
        body: JSON.stringify({
          shipping_address: address
        })
      })
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to add shipping address')
      }
      
      // Update cookie with the cart ID
      setCartCookie(effectiveCartId)
      
      return response.data
    } catch (err: any) {
      error.value = err.message || 'Failed to add shipping address'
      toast.add({
        title: 'Error',
        description: `Failed to add shipping address: ${err.message}`,
        color: 'red'
      })
      throw err
    } finally {
      isLoading.value = false
    }
  }
  
  const getShippingOptions = async (cartId: string) => {
    isLoading.value = true
    error.value = null
    
    try {
      // If cartId wasn't provided, try to get it from cookie
      let effectiveCartId = cartId
      if (!effectiveCartId) {
        const cookieCartId = getCartCookie()
        if (!cookieCartId) {
          throw new Error('No cart ID available')
        }
        effectiveCartId = cookieCartId
      }
      
      // Use cloudflareApiFetch instead of direct Medusa API
      const response = await cloudflareApiFetch(`/store/carts/${effectiveCartId}/shipping-options`)
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to get shipping options')
      }
      
      // Map the response to ensure compatibility with frontend interface
      const shippingOptions = (response.data || []).map((option: any) => ({
        ...option,
        amount: option.price || option.amount || 0, // Map price to amount for compatibility
        data: option.data || option.metadata || {} // Ensure data field exists
      }))
      
      return shippingOptions
    } catch (err: any) {
      error.value = err.message || 'Failed to get shipping options'
      toast.add({
        title: 'Error',
        description: `Failed to get shipping options: ${err.message}`,
        color: 'red'
      })
      throw err
    } finally {
      isLoading.value = false
    }
  }
  
  const addShippingMethod = async (cartId: string, optionId: string) => {
    isLoading.value = true
    error.value = null
    
    try {
      // If cartId wasn't provided, try to get it from cookie
      let effectiveCartId = cartId
      if (!effectiveCartId) {
        const cookieCartId = getCartCookie()
        if (!cookieCartId) {
          throw new Error('No cart ID available')
        }
        effectiveCartId = cookieCartId
      }
      
      // Use cloudflareApiFetch instead of direct Medusa API
      const response = await cloudflareApiFetch(`/store/carts/${effectiveCartId}/shipping-methods`, {
        method: 'POST',
        body: JSON.stringify({
          option_id: optionId
        })
      })
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to add shipping method')
      }
      
      // Update cookie with the cart ID
      setCartCookie(effectiveCartId)
      
      return response.data
    } catch (err: any) {
      error.value = err.message || 'Failed to add shipping method'
      toast.add({
        title: 'Error',
        description: `Failed to add shipping method: ${err.message}`,
        color: 'red'
      })
      throw err
    } finally {
      isLoading.value = false
    }
  }
  
  const getPaymentOptions = async (cartId: string) => {
    isLoading.value = true
    error.value = null
    
    try {
      // If cartId wasn't provided, try to get it from cookie
      let effectiveCartId = cartId
      if (!effectiveCartId) {
        const cookieCartId = getCartCookie()
        if (!cookieCartId) {
          throw new Error('No cart ID available')
        }
        effectiveCartId = cookieCartId
      }
      
      // Use cloudflareApiFetch to get payment providers from our simplified backend
      const response = await cloudflareApiFetch(`/store/carts/${effectiveCartId}/payment-sessions`)
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to get payment options')
      }
      
      // Map the response to our frontend format
      const paymentOptions = (response.payment_sessions || []).map((session: any) => {
        // Use provider_name from backend if available, otherwise fall back to provider_id
        const displayName = session.provider_name || session.provider_id || 'Unknown Payment Method'
        const description = session.provider_description || ''
        
        return {
          id: session.provider_id,
          name: displayName,
          data: {
            description: description,
            session_id: session.id,
            client_secret: session.data?.client_secret, // For Stripe payments
            // Include translation keys for the component (optional for i18n)
            nameKey: `payment.provider_names.${session.provider_id}`,
            descriptionKey: session.provider_id.includes('stripe') ? 'payment.credit_card_description' :
                           session.provider_id.includes('cash_on_delivery') ? 'payment.cash_on_delivery_description' : 
                           session.provider_id.includes('manual') ? 'payment.manual_payment_description' : ''
          }
        }
      })
      
      return paymentOptions
    } catch (err: any) {
      error.value = err.message || 'Failed to get payment options'
      console.error('Error fetching payment options:', err)
      
      // Fallback to default options if the backend call fails
      return [
        { 
          id: 'stripe', 
          name: 'Credit Card (powered by Stripe)', 
          data: { description: 'Pay securely with your credit card' } 
        },
        { 
          id: 'cash_on_delivery', 
          name: 'Cash on Delivery', 
          data: { description: 'Pay when your order is delivered' } 
        },
        { 
          id: 'manual', 
          name: 'Bank Transfer', 
          data: { description: 'Pay via bank transfer (manual processing)' } 
        }
      ]
    } finally {
      isLoading.value = false
    }
  }
  
  const addPaymentMethod = async (cartId: string, providerId: string) => {
    isLoading.value = true
    error.value = null
    
    try {
      // Use Cloudflare API
      // If cartId wasn't provided, try to get it from cookie
      let effectiveCartId = cartId
      if (!effectiveCartId) {
        const cookieCartId = getCartCookie()
        if (!cookieCartId) {
          throw new Error('No cart ID available')
        }
        effectiveCartId = cookieCartId
      }

      console.log('Selecting payment provider:', providerId, 'for cart:', effectiveCartId)

      // Use the Cloudflare backend to select payment provider
      const response = await cloudflareApiFetch(`/store/carts/${effectiveCartId}/payment-sessions/${providerId}`, {
        method: 'POST'
      })

      if (!response.success) {
        throw new Error(response.error || 'Failed to select payment provider')
      }

      console.log('Successfully selected payment provider:', providerId)

      // Update cookie with the cart ID
      setCartCookie(effectiveCartId)

      return response.data
    } catch (err: any) {
      error.value = err.message || 'Failed to add payment method'
      console.error('Error adding payment method:', err);
      toast.add({
        title: 'Error',
        description: `Failed to add payment method: ${err.message}`,
        color: 'red'
      })
      throw err
    } finally {
      isLoading.value = false
    }
  }
  
  // Removed unused functions: getPaymentSession, prepareCartForStripeCheckout
  
  // Complete the checkout process
  const completeCheckout = async (cartId: string) => {
    isLoading.value = true
    error.value = null
    
    try {
      console.log(`Completing checkout for cart ${cartId}`)
      
      // Before completing, verify that a payment method is selected
      try {
        const cartData = await retrieveCart(cartId, true) // Force refresh
        console.log('Cart data before completion:', cartData)
        
        const paymentSessions = cartData?.payment_sessions || []
        const selectedSession = paymentSessions.find((s: any) => s.is_selected === true)
        
        console.log('Payment sessions before completion:', paymentSessions.map((s: any) => ({ 
          id: s.id, 
          provider_id: s.provider_id, 
          is_selected: s.is_selected,
          status: s.status
        })))
        
        if (!selectedSession) {
          console.error('ERROR: No payment method selected before attempting checkout completion')
          console.error('Available payment sessions:', paymentSessions)
          throw new Error('No payment method selected. Please select a payment method before completing checkout.')
        } else {
          console.log(`✓ Payment method confirmed: ${selectedSession.provider_id} (${selectedSession.id})`)
        }
      } catch (verificationError) {
        console.error('Error verifying payment selection before completion:', verificationError)
        // Continue with completion attempt but log the issue
      }
      
      // Add a delay to ensure any payment processing has completed
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Complete the cart using cloudflareApiFetch
      const response = await cloudflareApiFetch(`/store/carts/${cartId}/complete`, {
        method: 'POST'
      })
      
      console.log(`Complete cart response:`, response)
      
      if (!response.success) {
        // Provide more detailed error information
        const errorMessage = response.error || 'Failed to complete checkout'
        console.error('Checkout completion failed:', {
          error: errorMessage,
          cartId: cartId,
          response: response
        })
        throw new Error(errorMessage)
      }
      
      // Parse the response data
      const result = response.data
      
      if (result?.type === 'order' && result?.order?.id) {
        // Clear the cart cookie on successful order creation
        removeCartCookie()
        
        console.log(`Successfully created order: ${result.order.id}`)
        return {
          type: 'order',
          data: result.order
        }
      } else if (result?.order?.id) {
        // Handle case where order is directly in result
        removeCartCookie()
        
        console.log(`Successfully created order: ${result.order.id}`)
        return {
          type: 'order',
          data: result.order
        }
      } else {
        console.error('Cart completion did not return an order:', result)
        return {
          type: 'cart',
          error: {
            message: 'Checkout did not create an order'
          }
        }
      }
    } catch (err: any) {
      error.value = err.message || 'Failed to complete checkout'
      console.error('Error completing checkout:', err)
      return {
        type: 'cart',
        error: {
          message: err.message || 'Failed to complete checkout'
        }
      }
    } finally {
      isLoading.value = false
    }
  }
  
  // Remove cart ID cookie
  const removeCartCookie = () => {
    console.log('Removing cart cookie');
    const cartCookie = useCookie('cart-id', {
      maxAge: 0, // Expire immediately
      path: '/',
      sameSite: 'lax'
    });
    cartCookie.value = null;
  }

  // Removed unused functions: applyDiscount, getPaymentProviders

  // Initialize payment collection and sessions for a cart
  const initializePaymentSessions: InitializePaymentSessionsFunction = async (cartId: string) => {
    isLoading.value = true
    error.value = null
    
    // Check if there's already a pending initialization for this cart
    if (!initializePaymentSessions.pendingInitializations) {
      initializePaymentSessions.pendingInitializations = new Map();
    }
    
    try {
      // Check if there's already a pending initialization for this cart
      if (initializePaymentSessions.pendingInitializations.has(cartId)) {
        console.log(`Payment session initialization already in progress for cart ${cartId}, waiting for it to complete`)
        return await initializePaymentSessions.pendingInitializations.get(cartId)
      }
      
      // Create a promise for cart initialization
      const initializationPromise = (async () => {
        console.log(`Initializing payment sessions for cart ${cartId}`)
        
        // Use our simplified backend endpoint
        const response = await cloudflareApiFetch(`/store/carts/${cartId}/payment-sessions`, {
          method: 'POST'
        })
        
        if (!response.success) {
          throw new Error(response.error || 'Failed to initialize payment sessions')
        }
        
        console.log(`Payment sessions initialized for cart ${cartId}`)
        return response
      })()
      
      // Store the promise in our map
      initializePaymentSessions.pendingInitializations.set(cartId, initializationPromise)
      
      // Wait for the initialization to complete
      const result = await initializationPromise
      
      // Remove this cart from the pending map
      initializePaymentSessions.pendingInitializations.delete(cartId)
      
      return result
    } catch (err: any) {
      console.error('Error initializing payment sessions:', err)
      error.value = err.message || 'Failed to initialize payment sessions'
      
      // Clean up pending initialization on error
      if (initializePaymentSessions.pendingInitializations) {
        initializePaymentSessions.pendingInitializations.delete(cartId)
      }
      
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Select payment provider for a cart
  const selectPaymentProvider: SelectPaymentProviderFunction = async (cartId: string, providerId: string) => {
    isLoading.value = true
    error.value = null
    
    // Static variable to track pending selections
    if (!selectPaymentProvider.pendingSelections) {
      selectPaymentProvider.pendingSelections = new Map();
    }
    
    const selectionKey = `${cartId}:${providerId}`;
    
    try {
      // Check if there's already a pending selection for this cart/provider combination
      if (selectPaymentProvider.pendingSelections.has(selectionKey)) {
        console.log(`Payment provider selection already in progress for ${selectionKey}, waiting for it to complete`);
        return await selectPaymentProvider.pendingSelections.get(selectionKey);
      }
      
      // Create a promise for this selection process
      const selectionPromise = (async () => {
        // If cartId wasn't provided, try to get it from cookie
        let effectiveCartId = cartId
        if (!effectiveCartId) {
          const cookieCartId = getCartCookie()
          if (!cookieCartId) {
            throw new Error('No cart ID available')
          }
          effectiveCartId = cookieCartId
        }
        
        console.log(`Selecting payment provider ${providerId} for cart ${effectiveCartId}`)
        
        // First, check current payment sessions to see the state before selection
        try {
          const cartData = await retrieveCart(effectiveCartId)
          console.log('Payment sessions before selection:', cartData?.payment_sessions?.map(s => ({ 
            id: s.id, 
            provider_id: s.provider_id, 
            is_selected: s.is_selected 
          })))
        } catch (e) {
          console.warn('Could not retrieve cart before payment selection:', e)
        }
        
        // Use our simplified backend endpoint
        const response = await cloudflareApiFetch(`/store/carts/${effectiveCartId}/payment-sessions/${providerId}`, {
          method: 'POST'
        })
        
        console.log(`Payment provider selection response:`, response)
        
        if (!response.success) {
          console.error(`Payment provider selection failed:`, response)
          throw new Error(response.error || 'Failed to select payment provider')
        }
        
        // Wait a moment and then verify the selection was applied
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        try {
          const updatedCartData = await retrieveCart(effectiveCartId, true) // Force refresh
          console.log('Payment sessions after selection:', updatedCartData?.payment_sessions?.map(s => ({ 
            id: s.id, 
            provider_id: s.provider_id, 
            is_selected: s.is_selected 
          })))
          
          const selectedSession = updatedCartData?.payment_sessions?.find(s => s.is_selected === true)
          if (!selectedSession) {
            console.warn(`WARNING: No payment session shows as selected after selection attempt for provider ${providerId}`)
            console.warn('This may cause "No payment method selected" error during checkout completion')
          } else if (selectedSession.provider_id !== providerId) {
            console.warn(`WARNING: Different provider selected (${selectedSession.provider_id}) than requested (${providerId})`)
          } else {
            console.log(`✓ Payment provider ${providerId} successfully selected and verified`)
          }
        } catch (e) {
          console.warn('Could not verify payment selection:', e)
        }
        
        console.log(`Successfully selected payment provider ${providerId} for cart ${effectiveCartId}`)
        return response
      })()
      
      // Store the promise in our map
      selectPaymentProvider.pendingSelections.set(selectionKey, selectionPromise)
      
      // Wait for the selection to complete
      const result = await selectionPromise
      
      // Remove this selection from the pending map
      selectPaymentProvider.pendingSelections.delete(selectionKey)
      
      return result
    } catch (err: any) {
      console.error('Error selecting payment provider:', err)
      error.value = err.message || 'Failed to select payment provider'
      
      // Clean up pending selection on error
      if (selectPaymentProvider.pendingSelections) {
        selectPaymentProvider.pendingSelections.delete(selectionKey)
      }
      
      throw err
    } finally {
      isLoading.value = false
    }
  }

  // Authorize a payment session
  const authorizePaymentSession = async (paymentCollectionId: string, sessionId: string) => {
    isLoading.value = true
    error.value = null
    
    try {
      console.log(`Authorizing payment session ${sessionId} for collection ${paymentCollectionId}`)
      
      // Use Cloudflare API - authorization is handled differently
      console.log('Skipping session authorization, proceeding directly to cart completion')

      return true
    } catch (err: any) {
      error.value = err.message || 'Failed to authorize payment session'
      console.error('Error authorizing payment session:', err)
      return false
    } finally {
      isLoading.value = false
    }
  }

  // Removed unused functions: directStripePaymentCompletion, getVariantPrice, getVariantStock, isVariantInStock, getVariantCurrency

  return {
    isLoading,
    error,

    // Cart functions (USED)
    retrieveCart,
    createCart,
    updateCart,
    getCartCookie,
    setCartCookie,
    removeCartCookie,

    // Customer functions (USED)
    getCustomer,
    getCustomerAddresses,

    // Region and shipping (USED)
    getRegions,
    getShippingOptions,
    addShippingMethod,

    // Payment functions (USED)
    getPaymentOptions,
    initializePaymentSessions,
    selectPaymentProvider,
    authorizePaymentSession,
    addPaymentMethod,
    completeCheckout,

    // Line item management (USED)
    addToCart,
    updateLineItem,
    removeLineItem,

    // Products (USED)
    getProduct,
    getVariant,
    getBatchVariants,

    // Collections (USED)
    getCollections,
    getCollection,

    // Order functions (USED)
    retrieveOrder,

    // Checkout methods (USED)
    addShippingAddress
  }
}