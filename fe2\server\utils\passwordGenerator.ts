import crypto from 'crypto'

/**
 * Cache for generated passwords to ensure consistency
 */
const passwordCache = new Map<string, string>()

/**
 * Generate a secure password for Medusa based on user ID
 * NOTE: This may be deprecated with the new Cloudflare backend authentication system
 */
export function generateSecureMedusaPassword(userId: string, serverSecret?: string): string {
  const secret = process.env.PASSWORD_SECRET || crypto.createHash('sha256').update('development').digest('hex')
  const hmac = crypto.createHmac('sha256', secret)
  hmac.update(userId)
  const hash = hmac.digest('hex')
  
  // Character sets for password generation
  const uppercaseChars = 'ABCDEFGHJKLMNPQRSTUVWXYZ'
  const lowercaseChars = 'abcdefghijkmnopqrstuvwxyz'
  const numberChars = '23456789'
  const specialChars = '!@#$%^&*_+-='
  
  // Extract parts from hash for deterministic randomness
  const upperPart = parseInt(hash.substring(0, 8), 16)
  const lowerPart = parseInt(hash.substring(8, 16), 16)
  const numberPart = parseInt(hash.substring(16, 24), 16)
  const specialPart = parseInt(hash.substring(24, 32), 16)
  
  let password = ''
  
  // Generate password parts
  for (let i = 0; i < 3 + (upperPart % 3); i++) {
    password += uppercaseChars[upperPart % uppercaseChars.length]
  }
  for (let i = 0; i < 3 + (lowerPart % 3); i++) {
    password += lowercaseChars[lowerPart % lowercaseChars.length]
  }
  for (let i = 0; i < 3 + (numberPart % 3); i++) {
    password += numberChars[numberPart % numberChars.length]
  }
  for (let i = 0; i < 2 + (specialPart % 2); i++) {
    password += specialChars[specialPart % specialChars.length]
  }
  
  // Shuffle the password using the hash
  const passwordArray = password.split('')
  for (let i = passwordArray.length - 1; i > 0; i--) {
    const j = parseInt(hash.charAt(i % hash.length) + hash.charAt((i + 1) % hash.length), 16) % (i + 1)
    ;[passwordArray[i], passwordArray[j]] = [passwordArray[j], passwordArray[i]]
  }
  
  return passwordArray.join('')
}

/**
 * Get or create a secure password for a user ID with caching
 * NOTE: This may be deprecated with the new Cloudflare backend authentication system
 */
export function getOrCreateSecurePassword(userId: string, serverSecret?: string): string {
  if (!passwordCache.has(userId)) {
    passwordCache.set(userId, generateSecureMedusaPassword(userId, serverSecret))
  }
  return passwordCache.get(userId)!
} 