import { computed } from 'vue'

export const useConfig = () => {
  const config = useRuntimeConfig()
  
  // Cloudflare API config
  const apiConfig = computed(() => ({
    baseUrl: config.public.cloudflareApiUrl || process.env.CLOUDFLARE_API_URL || 'http://localhost:8787',
  }))
  
  // Cognito related config
  const cognitoConfig = computed(() => ({
    userPoolId: config.public.cognitoUserPoolId || process.env.COGNITO_USER_POOL_ID || '',
    clientId: config.public.cognitoClientId || process.env.COGNITO_CLIENT_ID || '',
    region: config.public.cognitoRegion || process.env.COGNITO_REGION || 'us-east-1',
  }))
  
  const cloudflareConfig = computed(() => ({
    baseUrl: config.public.cloudflareApiUrl || config.public.apiUrl || 'http://localhost:8787',
  }))
  
  // Application config (anything not specific to a service)
  const appConfig = computed(() => ({
    apiBaseUrl: config.public.apiBaseUrl || '/api',
    storeName: config.public.storeName || 'HandmadeIn',
    storeUrl: config.public.storeUrl || config.public.serverUrl || 'http://localhost:3001',
    defaultLocale: config.public.defaultLocale || 'ro',
    defaultCurrency: config.public.defaultCurrency || 'RON',
  }))
  
  return {
    apiConfig,
    cognitoConfig,
    cloudflareConfig,
    appConfig
  }
}
