import { ref, computed } from 'vue'
import { useRuntimeConfig } from '#imports'

const cloudflareApiFetch = async (endpoint: string, options: RequestInit = {}) => {
  const headers = {
    'Content-Type': 'application/json',
    'Accept-Language': 'ro',
    'x-locale': 'ro',
    ...options.headers,
  }
  
  const baseUrl = 'http://localhost:8787'
  const url = endpoint.startsWith('http') ? endpoint : `${baseUrl}${endpoint}`
  
  try {
    const response = await fetch(url, {
      ...options,
      headers,
    })
    
    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'An unknown error occurred' }))
      console.error(`API Error (${response.status}):`, error)
      throw new Error(error.message || 'Request failed')
    }
    
    const data = await response.json()
    return data
  } catch (error) {
    console.error(`Error fetching ${url}:`, error)
    throw error
  }
}

// Checkout related interfaces
export interface Customer {
  id?: string
  email: string
  first_name: string
  last_name: string
  billing_address?: Address
  shipping_address?: Address
  phone?: string
  has_account: boolean
}

export interface Address {
  id?: string
  first_name: string
  last_name: string
  company?: string
  address_1: string
  address_2?: string
  city: string
  country_code: string
  province?: string
  postal_code: string
  phone?: string
  email?: string
}

export interface ShippingOption {
  id: string
  name: string
  price: number
  estimated_delivery?: string
  available_cities?: string[]
}

export interface PaymentOption {
  id: string
  provider_id: string
  name: string
  description?: string
  enabled: boolean
}

// Import Order interface from useOrder to avoid duplication
import type { Order } from './useOrder'

export function useCheckout() {
  const loading = ref(false)
  const error = ref<string | null>(null)
  const checkoutStep = ref(0)
  const checkoutError = ref<string | null>(null)
  const shippingOptions = ref<ShippingOption[]>([])
  const selectedShippingOption = ref<string | null>(null)
  const paymentOptions = ref<PaymentOption[]>([])
  const selectedPaymentOption = ref<string | null>(null)
  const checkoutComplete = ref(false)
  const orderConfirmation = ref<Order | null>(null)
  const processingPayment = ref(false)

  const config = useRuntimeConfig()

  // Cart ID management
  const cartId = ref<string | null>(null)

  // Initialize cart ID from localStorage
  const initCartId = () => {
    if (process.client) {
      cartId.value = localStorage.getItem('cart_id') || 
                    localStorage.getItem('medusaCartId') || 
                    localStorage.getItem('medusa_cart_id')
    }
  }

  // Set cart ID
  const setCartId = (id: string) => {
    cartId.value = id
    if (process.client) {
      localStorage.setItem('cart_id', id)
      localStorage.setItem('medusaCartId', id)
      localStorage.setItem('medusa_cart_id', id)
    }
  }

  // Clear cart
  const clearCart = () => {
    cartId.value = null
    if (process.client) {
      localStorage.removeItem('cart_id')
      localStorage.removeItem('medusaCartId')
      localStorage.removeItem('medusa_cart_id')
    }
  }

  // Initialize on mount
  if (process.client) {
    initCartId()
  }

  /**
   * Get authentication token
   */
  const getToken = (): string | null => {
    if (process.server) return null
    
    try {
      const authData = localStorage.getItem('auth')
      if (authData) {
        const parsed = JSON.parse(authData)
        if (parsed && parsed.isAuthenticated && parsed.accessToken) {
          return parsed.accessToken
        }
      }
    } catch (e) {
      console.error('Failed to get auth token:', e)
    }
    
    return null
  }

  // Checkout functions
  const addShippingAddress = async ({ cartId, address }: { cartId: string, address: any }) => {
    try {
      loading.value = true
      error.value = null
      
      // Save customer email for later use
      localStorage.setItem('checkout_email', address.email)
      
      const token = getToken()
      const headers: Record<string, string> = {}
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }
      
      const response = await cloudflareApiFetch(`/store/cart/${cartId}`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          shipping_address: address,
          email: address.email
        })
      })
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to update shipping address')
      }
      
      loading.value = false
      return response.data
    } catch (err: any) {
      loading.value = false
      error.value = err.message || 'Error adding shipping address'
      console.error('Error adding shipping address:', err)
      throw err
    }
  }

  const getShippingOptions = async ({ cartId }: { cartId: string }) => {
    try {
      loading.value = true
      error.value = null
      
      const response = await cloudflareApiFetch(`/store/cart/${cartId}/shipping-options`, {
        method: 'GET'
      })
      
      if (!response.success) {
        throw new Error(response.error || 'No shipping options available')
      }
      
      shippingOptions.value = response.data || []
      loading.value = false
      return response.data
    } catch (err: any) {
      loading.value = false
      error.value = err.message || 'Error fetching shipping options'
      console.error('Error fetching shipping options:', err)
      throw err
    }
  }

  const addShippingMethod = async ({ cartId, optionId }: { cartId: string, optionId: string }) => {
    try {
      loading.value = true
      error.value = null
      
      selectedShippingOption.value = optionId
      
      const token = getToken()
      const headers: Record<string, string> = {}
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }
      
      const response = await cloudflareApiFetch(`/store/cart/${cartId}/shipping-methods`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          option_id: optionId
        })
      })
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to add shipping method')
      }
      
      // Update cart in store
      setCartId(response.data.cart_id)
      
      loading.value = false
      return response.data
    } catch (err: any) {
      loading.value = false
      error.value = err.message || 'Error adding shipping method'
      console.error('Error adding shipping method:', err)
      throw err
    }
  }

  const getPaymentOptions = async () => {
    try {
      loading.value = true
      error.value = null
      
      const response = await cloudflareApiFetch('/store/payment-methods', {
        method: 'GET'
      })
      
      if (response.success && response.data) {
        paymentOptions.value = response.data
      } else {
        // Fallback to default payment options
        paymentOptions.value = [
          {
            id: 'stripe',
            provider_id: 'stripe',
            name: 'Credit/Debit Card',
            description: 'Pay securely with your credit or debit card',
            enabled: true,
          },
          {
            id: 'cash_on_delivery',
            provider_id: 'cash_on_delivery',
            name: 'Cash on Delivery',
            description: 'Pay when you receive your order',
            enabled: true,
          },
        ]
      }
      
      loading.value = false
      return paymentOptions.value
    } catch (err: any) {
      loading.value = false
      error.value = err.message || 'Error fetching payment options'
      console.error('Error fetching payment options:', err)
      
      // Return default options on error
      paymentOptions.value = [
        {
          id: 'stripe',
          provider_id: 'stripe',
          name: 'Credit/Debit Card',
          description: 'Pay securely with your credit or debit card',
          enabled: true,
        },
        {
          id: 'cash_on_delivery',
          provider_id: 'cash_on_delivery',
          name: 'Cash on Delivery',
          description: 'Pay when you receive your order',
          enabled: true,
        },
      ]
      
      return paymentOptions.value
    }
  }

  const addPaymentMethod = async ({ cartId, providerId, data = {} }: { cartId: string, providerId: string, data?: any }) => {
    try {
      loading.value = true
      error.value = null
      
      selectedPaymentOption.value = providerId
      
      const token = getToken()
      const headers: Record<string, string> = {}
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }
      
      const response = await cloudflareApiFetch(`/store/cart/${cartId}/payment-sessions`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          provider_id: providerId,
          data
        })
      })
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to add payment method')
      }
      
      loading.value = false
      return response.data
    } catch (err: any) {
      loading.value = false
      error.value = err.message || 'Error adding payment method'
      console.error('Error adding payment method:', err)
      throw err
    }
  }

  const completeCheckout = async ({ cartId }: { cartId: string }) => {
    try {
      loading.value = true
      processingPayment.value = true
      error.value = null
      
      const token = getToken()
      const headers: Record<string, string> = {}
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }
      
      const response = await cloudflareApiFetch(`/store/cart/${cartId}/complete`, {
        method: 'POST',
        headers
      })
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to complete checkout')
      }
      
      orderConfirmation.value = response.data
      checkoutComplete.value = true
      
      // Clear cart after successful checkout
      clearCart()
      
      loading.value = false
      processingPayment.value = false
      return response.data
    } catch (err: any) {
      loading.value = false
      processingPayment.value = false
      error.value = err.message || 'Error completing checkout'
      console.error('Error completing checkout:', err)
      throw err
    }
  }

  const applyDiscount = async ({ cartId, code }: { cartId: string, code: string }) => {
    try {
      loading.value = true
      error.value = null
      
      const token = getToken()
      const headers: Record<string, string> = {}
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }
      
      const response = await cloudflareApiFetch(`/store/cart/${cartId}/discounts`, {
        method: 'POST',
        headers,
        body: JSON.stringify({ code })
      })
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to apply discount')
      }
      
      // Update cart in store
      setCartId(response.data.cart_id)
      
      loading.value = false
      return response.data
    } catch (err: any) {
      loading.value = false
      error.value = err.message || 'Error applying discount'
      console.error('Error applying discount:', err)
      throw err
    }
  }

  const removeDiscount = async ({ cartId, code }: { cartId: string, code: string }) => {
    try {
      loading.value = true
      error.value = null
      
      const token = getToken()
      const headers: Record<string, string> = {}
      if (token) {
        headers['Authorization'] = `Bearer ${token}`
      }
      
      const response = await cloudflareApiFetch(`/store/cart/${cartId}/discounts/${code}`, {
        method: 'DELETE',
        headers
      })
      
      if (!response.success) {
        throw new Error(response.error || 'Failed to remove discount')
      }
      
      // Update cart in store
      setCartId(response.data.cart_id)
      
      loading.value = false
      return response.data
    } catch (err: any) {
      loading.value = false
      error.value = err.message || 'Error removing discount'
      console.error('Error removing discount:', err)
      throw err
    }
  }

  const resetCheckout = () => {
    checkoutStep.value = 0
    checkoutError.value = null
    selectedShippingOption.value = null
    selectedPaymentOption.value = null
    checkoutComplete.value = false
    orderConfirmation.value = null
    processingPayment.value = false
    error.value = null
  }

  return {
    // State
    loading,
    error,
    checkoutStep,
    checkoutError,
    shippingOptions,
    selectedShippingOption,
    paymentOptions,
    selectedPaymentOption,
    checkoutComplete,
    orderConfirmation,
    processingPayment,
    cartId,

    // Actions
    addShippingAddress,
    getShippingOptions,
    addShippingMethod,
    getPaymentOptions,
    addPaymentMethod,
    completeCheckout,
    applyDiscount,
    removeDiscount,
    resetCheckout,

    // Utils
    getToken
  }
} 