<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useMedusaService } from '~/composables/useMedusaService'
import { useRecentProductsStore } from '~/stores/recentProducts'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'

// Define interfaces for type safety
interface Product {
  id: string;
  title: string;
  handle: string;
  description?: string;
  thumbnail?: string;
  price?: number;
  collection?: {
    title: string;
  };
  variants?: Array<{
    id: string;
    title?: string;
    prices?: Array<{
      amount: number;
      currency_code: string;
    }>;
    inventory_quantity?: number;
    inventory_items?: any[];
    calculated_price?: {
      calculated_amount: number;
      currency_code: string;
    };
  }>;
  tags?: string[];
  images?: Array<{
    id: string;
    url: string;
  }>;
}

interface Collection {
  id: string;
  title: string;
  handle: string;
  description?: string;
  thumbnail?: string;
  products?: Product[];
  product_count?: number;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  collection_image?: {
    id: string;
    image_url: string;
  };
  images?: Array<{
    id: string;
    image_url: string;
  }>;
}

// Define interface for variant details
interface VariantDetail {
  id: string;
  title?: string;
  prices?: Array<{
    amount: number;
    currency_code: string;
  }>;
  inventory_quantity?: number;
  inventory_items?: any[];
  calculated_price?: {
    calculated_amount: number;
    currency_code: string;
  };
}

const route = useRoute()
const router = useRouter()
const medusaService = useMedusaService()
const recentProductsStore = useRecentProductsStore()
const { t } = useI18n()

const products = ref<Product[]>([])
const collections = ref<Collection[]>([])
const collection = ref<Collection>()
const currentCategory = ref<string>('')
const loading = ref(true)
const apiLoading = ref(false)
const error = ref<string | null>(null)
const pageSize = ref(12)
const currentPage = ref(1)
const totalProducts = ref(0)

const loadProducts = async (page = 1) => {
  if (!collection.value) return
  
  currentPage.value = page
  loading.value = true
  apiLoading.value = true
  
  try {
    // If the collection already has products from the initial fetch, use them
    if (page === 1 && collection.value.products && collection.value.products.length > 0) {
      products.value = collection.value.products.map((product: any) => {
        // If thumbnail is null but there are images, use the first image as thumbnail
        if (!product.thumbnail && product.images && product.images.length > 0) {
          product.thumbnail = product.images[0].url
        }
        return product
      })
      
      totalProducts.value = collection.value.product_count || collection.value.products.length
      
      // No need to fetch batch variants anymore since collections endpoint provides pricing and stock
    } else {
      // For other pages or if no products in collection, fetch separately
      const { getProducts } = useProduct()
      const result = await getProducts({
        collection_id: currentCategory.value,
        limit: pageSize.value,
        offset: (page - 1) * pageSize.value
      })
      
      if (result && result.products) {
        // Make sure we process products to fix missing thumbnails
        products.value = result.products.map((product: any) => {
          // If thumbnail is null but there are images, use the first image as thumbnail
          if (!product.thumbnail && product.images && product.images.length > 0) {
            product.thumbnail = product.images[0].url
          }
          return product
        })
        
        // Set the total count from the API response
        totalProducts.value = result.count || 0
        
        // No need to fetch batch variants anymore since collections endpoint provides pricing and stock
      } else {
        products.value = []
        totalProducts.value = 0
      }
    }
  } catch (err) {
    console.error('Error loading products:', err)
    error.value = 'Failed to load products'
  } finally {
    loading.value = false
    apiLoading.value = false
  }
}

const totalPages = computed(() => Math.ceil(totalProducts.value / pageSize.value))

const goToPage = (page: number) => {
  if (page < 1 || page > totalPages.value) return
  loadProducts(page)
  
  // Scroll to top
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

onMounted(async () => {
  try {
    // First try to load the collection directly using the slug for more reliable data
    const collectionData = await medusaService.getCollection(route.params.slug as string);
    
    if (collectionData) {
      // If collection is found, use it directly
      collection.value = collectionData;
      currentCategory.value = collectionData.id;
      await loadProducts(1);
    } else {
      // Fallback to the old method of collection lookup
      const collectionsData = await medusaService.getCollections();
      collections.value = collectionsData || [];
      collection.value = collections.value.find(c => c.handle === route.params.slug);
      
      if (collection.value) {
        currentCategory.value = collection.value.id;
        await loadProducts(1);
      } else {
        // Collection not found, redirect to collections page
        router.push('/collections');
      }
    }
  } catch (err) {
    console.error('Error loading collections:', err);
    error.value = 'Failed to load collections';
  }
})

useHead({
  title: () => `${collection.value?.title || 'Categorie'} - Handmade in RO`,
  meta: [
    { name: 'description', content: () => collection.value?.description || 'Explorează colecția noastră selectă' }
  ]
})
</script>

<template>
  <div class="min-h-screen bg-wheat-50">
    <!-- Breadcrumb -->
    <div class="bg-wheat-100 py-3 border-b border-wheat-200">
      <div class="container mx-auto px-4">
        <div class="flex items-center text-sm text-forest-600">
          <NuxtLink to="/" class="hover:text-forest-800">{{ $t('common.home') }}</NuxtLink>
          <span class="mx-2">/</span>
          <NuxtLink to="/collections" class="hover:text-forest-800">{{ $t('common.collections') }}</NuxtLink>
          <span class="mx-2">/</span>
          <span class="font-medium text-forest-800">{{ collection?.title || $t('common.category') }}</span>
        </div>
      </div>
    </div>
    
    <CategoryHero 
      :title="collection?.title" 
      :description="collection?.description"
      :image="collection?.collection_image?.image_url || (collection?.images?.[0]?.image_url) || collection?.thumbnail"
    />
    
    <div class="container mx-auto px-4 py-12">
      <!-- Category info -->
      <div class="mb-8">
        <div class="flex justify-between items-center">
          <h2 class="text-2xl font-serif font-bold text-forest-900">
            {{ $t('category.products_in_category', { title: collection?.title }) }}
          </h2>
          <button 
            @click="loadProducts(1)" 
            class="flex items-center gap-2 px-4 py-2 bg-forest-100 hover:bg-forest-200 text-forest-700 rounded-lg transition-colors"
            :disabled="loading"
          >
            <Icon 
              name="heroicons:arrow-path" 
              class="w-5 h-5"
              :class="{ 'animate-spin': loading }"
            />
            <span>{{ loading ? $t('category.loading') : $t('category.refresh') }}</span>
          </button>
        </div>
      </div>
      
      <!-- Loading state -->
      <div v-if="loading || apiLoading" class="loading-state">
        <div class="grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          <div v-for="i in 8" :key="i" class="animate-pulse bg-white rounded-xl p-4">
            <div class="aspect-square bg-wheat-200 rounded-lg mb-4"></div>
            <div class="h-4 bg-wheat-200 rounded w-3/4 mb-2"></div>
            <div class="h-4 bg-wheat-200 rounded w-1/2"></div>
          </div>
        </div>
      </div>
      
      <!-- Error state -->
      <div v-else-if="error" class="error-state">
        <div class="bg-red-50 text-red-600 p-8 rounded-lg text-center">
          <Icon name="heroicons:exclamation-triangle" class="w-16 h-16 text-red-400 mx-auto mb-4" />
          <h3 class="text-xl font-medium text-red-900 mb-2">{{ $t('category.error_loading_products') }}</h3>
          <p class="text-red-600 max-w-md mx-auto mb-4">
            {{ error }}
          </p>
          <button 
            @click="loadProducts(1)" 
            class="px-4 py-2 bg-red-100 hover:bg-red-200 text-red-700 rounded-lg transition-colors"
          >
            {{ $t('cart.try_again') }}
          </button>
        </div>
      </div>
      
      <!-- Empty state -->
      <div v-else-if="products.length === 0" class="empty-state text-center py-12">
        <Icon name="heroicons:shopping-bag" class="w-16 h-16 text-forest-300 mx-auto mb-4" />
        <h3 class="text-xl font-medium text-forest-900 mb-2">{{ $t('category.no_products_found') }}</h3>
        <p class="text-forest-600 max-w-md mx-auto mb-6">
          {{ $t('category.no_products_message') }}
        </p>
        <NuxtLink to="/collections" class="inline-flex items-center px-4 py-2 bg-forest-600 text-white rounded-lg hover:bg-forest-700 transition-colors">
          <Icon name="heroicons:arrow-left" class="w-5 h-5 mr-2" />
          {{ $t('category.browse_collections') }}
        </NuxtLink>
      </div>
      
      <!-- Products grid -->
      <div v-else class="products-grid">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          <ProductCard
            v-for="product in products"
            :key="product.id"
            :product="product"
            class="w-full"
          />
        </div>
        
        <!-- Pagination -->
        <div v-if="totalPages > 1" class="pagination flex justify-center mt-12">
          <div class="inline-flex items-center rounded-md shadow-sm">
            <button 
              @click="goToPage(currentPage - 1)" 
              :disabled="currentPage === 1"
              class="px-3 py-2 text-sm font-medium text-forest-700 bg-white border border-wheat-300 rounded-l-md hover:bg-wheat-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Icon name="heroicons:chevron-left" class="w-5 h-5" />
            </button>
            
            <div v-for="page in totalPages" :key="page" class="hidden md:block">
              <button 
                v-if="page === currentPage || page === 1 || page === totalPages || (page >= currentPage - 1 && page <= currentPage + 1)"
                @click="goToPage(page)"
                :class="[
                  'px-4 py-2 text-sm font-medium border border-wheat-300',
                  page === currentPage 
                    ? 'text-white bg-forest-600 border-forest-600 z-10' 
                    : 'text-forest-700 bg-white hover:bg-wheat-50'
                ]"
              >
                {{ page }}
              </button>
              <span 
                v-else-if="page === currentPage - 2 || page === currentPage + 2"
                class="px-4 py-2 text-sm font-medium text-forest-700 bg-white border border-wheat-300"
              >
                ...
              </span>
            </div>
            
            <div class="md:hidden px-4 py-2 text-sm font-medium text-forest-700 bg-white border border-wheat-300">
              {{ currentPage }} / {{ totalPages }}
            </div>
            
            <button 
              @click="goToPage(currentPage + 1)" 
              :disabled="currentPage === totalPages"
              class="px-3 py-2 text-sm font-medium text-forest-700 bg-white border border-wheat-300 rounded-r-md hover:bg-wheat-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Icon name="heroicons:chevron-right" class="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <CollectionHighlights class="py-12 bg-white" />
    
    <!-- Recently Viewed Products -->
    <RecentlyViewed v-if="recentProductsStore.sortedItems.length > 0" class="py-12 bg-wheat-100" />
  </div>
</template>

<style scoped>
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
