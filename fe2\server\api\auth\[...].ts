import { define<PERSON><PERSON><PERSON><PERSON><PERSON>, readBody, getQ<PERSON>y, create<PERSON><PERSON>r, getR<PERSON>er<PERSON><PERSON><PERSON>, setC<PERSON>ie, deleteCookie, parseCookies as h3ParseCookies, appendHeader } from 'h3';
import { createHmac } from 'crypto';
import jwt from 'jsonwebtoken';
// Import AWS SDK for direct Cognito access for all authentication operations
import { 
  CognitoIdentityProviderClient, 
  SignUpCommand, 
  InitiateAuthCommand, 
  ConfirmSignUpCommand, 
  ListUsersCommand, 
  ResendConfirmationCodeCommand, 
  GlobalSignOutCommand, 
  GetUserCommand,
  ForgotPasswordCommand,
  ConfirmForgotPasswordCommand,
  AdminGetUserCommand,
  UpdateUserAttributesCommand,
  ChangePasswordCommand
} from '@aws-sdk/client-cognito-identity-provider';

// Import H3Event type and our custom types
import type { H3Event } from 'h3';
// Define the types that would be in '@/types/auth'
interface CognitoResult {
  isSignedIn: boolean;
  nextStep?: any;
  userId?: string;
  username?: string;
  [key: string]: any;
}

interface MedusaResponse {
  authenticated: boolean;
  customer?: any;
  error?: string;
  token?: string;
}

// Fix the import to use Nuxt's runtime config instead of direct nitropack import
import { useRuntimeConfig } from '#imports';
import { AUTH_COOKIE_NAME, COOKIE_OPTIONS } from '~/constants/auth';

// Create a client with AWS region from runtime config or env
const getCognitoClient = () => {
  const config = useRuntimeConfig();
  const region = config.cognitoRegion || process.env.COGNITO_REGION || 'eu-central-1';
  
  return new CognitoIdentityProviderClient({
    region,
  });
};

const cognitoClient = getCognitoClient();

// Add a debug function to check the environment variables
const logCognitoConfig = () => {
  const config = useRuntimeConfig();
  console.log('Cognito Config:', {
    regionFromConfig: config.cognitoRegion,
    regionFromEnv: process.env.COGNITO_REGION,
    clientIdFromConfig: config.public?.cognitoClientId ? '[SET]' : '[NOT SET]',
    clientIdFromEnv: process.env.COGNITO_CLIENT_ID ? '[SET]' : '[NOT SET]',
    secretFromConfig: config.cognitoClientSecret ? '[SET]' : '[NOT SET]',
    secretFromEnv: process.env.COGNITO_CLIENT_SECRET ? '[SET]' : '[NOT SET]',
    userPoolFromConfig: config.cognitoUserPoolId ? '[SET]' : '[NOT SET]',
    userPoolFromEnv: process.env.COGNITO_USER_POOL_ID ? '[SET]' : '[NOT SET]',
  });
};

// Log on startup to help with debugging
logCognitoConfig();

/**
 * Calculate the SECRET_HASH value required by AWS Cognito
 * AWS Cognito expects: Base64(HMAC_SHA256(ClientSecret, Username + ClientId))
 * 
 * @param username The username (email)
 * @param clientId The Cognito app client ID
 * @param clientSecret The Cognito app client secret
 * @returns The calculated SECRET_HASH value
 */
function calculateSecretHash(username: string, clientId: string, clientSecret: string): string {
  try {
    // Ensure username is lowercase to maintain consistency
    const normalizedUsername = username.toLowerCase();
    
    // The message is the concatenation of username and clientId (no separator)
    const message = normalizedUsername + clientId;
    
    // Create the HMAC using SHA256 with the client secret as the key
    const hash = createHmac('sha256', clientSecret)
      .update(message)
      .digest('base64');
    
    return hash;
  } catch (error) {
    console.error('Error calculating SECRET_HASH:', error);
    throw new Error('Failed to calculate SECRET_HASH for authentication');
  }
}

// Use Cloudflare API instead of Medusa client
const cloudflareApiFetch = async (endpoint: string, options: RequestInit = {}) => {
  const config = useRuntimeConfig();
  const baseUrl = config.public.cloudflareApiUrl || 'http://localhost:8787';

  const headers = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  const url = endpoint.startsWith('http') ? endpoint : `${baseUrl}${endpoint}`;

  try {
    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'An unknown error occurred' }));
      throw new Error(error.message || 'Request failed');
    }

    return await response.json();
  } catch (error) {
    console.error('Cloudflare API fetch error:', error);
    throw error;
  }
};

// Function to generate SECRET_HASH with improved error handling
const generateSecretHash = (username: string): string => {
  try {
    const config = useRuntimeConfig();
    const clientId = config.public.cognitoClientId || process.env.COGNITO_CLIENT_ID;
    const clientSecret = config.cognitoClientSecret || process.env.COGNITO_CLIENT_SECRET;
    
    if (!clientId || !clientSecret) {
      console.error('Missing Cognito client ID or secret');
      return '';
    }
    
    const message = username + clientId;
    return createHmac('sha256', clientSecret)
      .update(message)
      .digest('base64');
  } catch (error) {
    console.error('Failed to generate secret hash:', error);
    return '';
  }
};

// Helper function to sync customer data between systems
const syncCustomerData = async (email: string, password: string, firstName?: string, lastName?: string, phone?: string) => {
  try {
    const medusa = getMedusaClient();
    
    // First try to authenticate
    try {
      await medusa.auth.authenticate({
        email,
        password
      });
      
      // Update customer info if provided
      if (firstName || lastName || phone) {
        await medusa.customers.update({
          first_name: firstName,
          last_name: lastName,
          phone
        });
      }
      
      return {
        success: true,
        action: 'updated'
      };
    } catch (authError) {
      // If authentication fails, create a new customer
      await medusa.customers.create({
        email,
        password,
        first_name: firstName || '',
        last_name: lastName || '',
        phone: phone || ''
      });
      
      return {
        success: true,
        action: 'created'
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error syncing customer data'
    };
  }
};

const handleSignUp = async (event: H3Event) => {
  const body = await readBody(event);
  const { 
    username, 
    password, 
    attributes, 
    firstName, 
    lastName, 
    phone,
    addressLine1, 
    addressLine2, 
    city, 
    state, 
    postalCode, 
    country 
  } = body;
  
  // Make sure we have the Cognito configuration
  const config = useRuntimeConfig();
  if (!config.cognitoUserPoolId || !config.cognitoClientId || !config.cognitoClientSecret) {
    throw createError({
      statusCode: 400,
      message: "Auth UserPool not configured. Missing Cognito configuration.",
    });
  }
  
  // When the user pool has email aliases enabled, we need to:
  // 1. Use a non-email format username
  // 2. Set the email as an attribute
  
  // Create a unique username that isn't in email format
  // Option 1: Use a UUID-based approach
  const uniqueUsername = `user_${Date.now()}_${Math.floor(Math.random() * 10000)}`;
  
  // Format address according to Cognito schema
  const addressObj = {
    formatted: `${addressLine1}, ${city}, ${state}, ${postalCode}, ${country}`,
    street_address: addressLine1,
    locality: city,
    region: state,
    postal_code: postalCode,
    country
  };
  
  // Format the required attributes according to Cognito schema
  const updatedAttributes = {
    ...(attributes || {}),
    email: username, // The username is actually an email in our form
    address: JSON.stringify(addressObj),
    locale: attributes?.locale || "en", // Default locale
    phone_number: phone || "",
    given_name: firstName || "",
    family_name: lastName || "",
    name: `${firstName || ""} ${lastName || ""}`.trim(),
  };
  
  // Generate the SECRET_HASH using our new function
  const secretHash = calculateSecretHash(
    uniqueUsername, 
    config.cognitoClientId, 
    config.cognitoClientSecret
  );
  
  try {
    // Use the AWS SDK directly to handle the SECRET_HASH
    const client = new CognitoIdentityProviderClient({
      region: config.cognitoRegion,
    });
    
    // Transform attributes to the format expected by the AWS SDK
    const userAttributes = Object.entries(updatedAttributes).map(([key, value]) => ({
      Name: key,
      Value: value as string,
    }));
    
    // Create the sign-up command with the SECRET_HASH
    const signUpCommand = new SignUpCommand({
      ClientId: config.cognitoClientId,
      Username: uniqueUsername,
      Password: password,
      SecretHash: secretHash,
      UserAttributes: userAttributes,
    });
    
    // Execute the sign-up command
    const cognitoResult = await client.send(signUpCommand);
    
    // Transform the result to match Amplify's format
    return {
      isSignUpComplete: cognitoResult.UserConfirmed,
      nextStep: {
        signUpStep: cognitoResult.UserConfirmed ? 'DONE' : 'CONFIRM_SIGN_UP',
      },
      userId: cognitoResult.UserSub,
      username: uniqueUsername, // Return the generated username for future operations
      email: username, // Also return the email for reference
    };
  } catch (error: any) {
    console.error("Signup error:", error);
    throw createError({
      statusCode: 400,
      message: error.message,
    });
  }
};

// Update the getAccessToken function to work properly on the server side
const getAccessToken = async (event?: H3Event, authResult?: any) => {
  try {
    // First, if we have a direct auth result (from sign-in), use that
    if (authResult && authResult.AccessToken) {
      return authResult.AccessToken;
    }
    
    const tokenStorageKey = 'amplify-signin-with-hostedUI-oauth2-access-token';
    
    // If we have an event, get the cookie from the request
    if (event) {
      const cookies = h3ParseCookies(event);
      const authCookie = cookies[AUTH_COOKIE_NAME];
      
      if (authCookie) {
        try {
          const authData = JSON.parse(authCookie);
          if (authData.accessToken) {
            return authData.accessToken;
          }
        } catch (e) {
          console.error('Error parsing auth cookie', e);
        }
      }
    }
    
    // When running on the client, this code will execute
    // Using process.client instead of checking for document
    if (process.client) {
      try {
        // For client-side access, try localStorage as fallback
        const storedToken = localStorage.getItem(tokenStorageKey);
        if (storedToken) {
          return storedToken;
        }
        
        // For backwards compatibility, try to use the AWS SDK
        // but avoid using window directly as it's not available in TypeScript definitions
        try {
          // @ts-ignore - We know this might not exist, but we're catching errors
          const globalAWS = globalThis.AWS;
          if (globalAWS && globalAWS.config && globalAWS.config.credentials) {
            return globalAWS.config.credentials.accessKeyId;
          }
        } catch (awsErr) {
          // Ignore this error, it's just a fallback attempt
        }
      } catch (err) {
        console.error('Error accessing client-side storage', err);
      }
    }
    
    return null;
  } catch (error) {
    console.error('Error getting access token', error);
    return null;
  }
};

// Fix the findUserByEmail function signature and implementation to clarify return type
const findUserByEmail = async (usernameOrEmail: string): Promise<any> => {
  try {
    const config = useRuntimeConfig();
    const userPoolId = config.cognitoUserPoolId || process.env.COGNITO_USER_POOL_ID;
    
    if (!userPoolId) {
      console.error('Missing Cognito user pool ID');
      return null;
    }
    
    const command = new ListUsersCommand({
      UserPoolId: userPoolId,
      Filter: usernameOrEmail.includes('@') 
        ? `email = "${usernameOrEmail}"` 
        : `username = "${usernameOrEmail}"`,
      Limit: 1
    });
    
    const response = await cognitoClient.send(command);
    if (response.Users && response.Users.length > 0) {
      return response.Users[0];
    }
    return null;
  } catch (error) {
    console.error('Error finding user by email', error);
    return null;
  }
};

// Fix handleSignIn function to use enhanced getAccessToken
const handleSignIn = async (event: H3Event) => {
  try {
    const body = await readBody(event);
    const username = body.username;
    const password = body.password;

    if (!username || !password) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Username and password are required'
      });
    }

    // Get the Cognito configuration
    const config = useRuntimeConfig();
    const clientId = config.public.cognitoClientId || process.env.COGNITO_CLIENT_ID;
    
    if (!clientId) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Missing Cognito client configuration'
      });
    }

    // Use the shared function to generate a consistent SECRET_HASH
    const secretHash = generateSecretHash(username);

    const signInCommand = new InitiateAuthCommand({
      AuthFlow: 'USER_PASSWORD_AUTH',
      ClientId: clientId,
      AuthParameters: {
        USERNAME: username,
        PASSWORD: password,
        SECRET_HASH: secretHash,
      },
    });

    console.log('Attempting to sign in user:', username);
    const result = await cognitoClient.send(signInCommand);
    console.log('Sign in result received, challenge name:', result.ChallengeName || 'none');

    if (result.ChallengeName) {
      return {
        success: false,
        nextChallenge: {
          challengeName: result.ChallengeName,
          challengeParameters: result.ChallengeParameters,
          session: result.Session
        }
      };
    }

    // Get tokens from the result
    const idToken = result.AuthenticationResult?.IdToken;
    const accessToken = result.AuthenticationResult?.AccessToken;
    const refreshToken = result.AuthenticationResult?.RefreshToken;
    const expiresIn = result.AuthenticationResult?.ExpiresIn;

    console.log('Authentication successful, access token received:', !!accessToken);

    // If we have an access token, get the user info
    let username_from_token = null;
    let userId = null;
    let userAttributes = {};

    if (accessToken) {
      try {
        const getUserCommand = new GetUserCommand({
          AccessToken: accessToken
        });

        const userResult = await cognitoClient.send(getUserCommand);
        username_from_token = userResult.Username;
        
        // Extract user attributes
        if (userResult.UserAttributes) {
          userAttributes = userResult.UserAttributes.reduce((attrs: any, attr) => {
            if (attr.Name && attr.Value) {
              attrs[attr.Name] = attr.Value;
              if (attr.Name === 'sub') {
                userId = attr.Value;
              }
            }
            return attrs;
          }, {});
        }
        console.log('User info retrieved, userId:', userId);
      } catch (userInfoError) {
        console.error('Error getting user info:', userInfoError);
      }
    }

    // Try to authenticate with Medusa as well
    let medusaResult: MedusaResponse = { authenticated: false };
    let medusaToken = null;

    try {
      const medusa = getMedusaClient();
      const response = await medusa.auth.authenticate({
        email: username,
        password
      });

      if (response.customer) {
        // Cast to any to handle the dynamic response structure
        const authResponse = response as any;
        medusaToken = authResponse.access_token;
        medusaResult = {
          customer: response.customer,
          authenticated: true,
          token: medusaToken
        };
        console.log('Medusa authentication successful, token received:', !!medusaToken);
      }
    } catch (medusaError) {
      console.log('Medusa authentication failed, but Cognito succeeded', medusaError);
      // Continue with Cognito auth even if Medusa fails
      medusaResult = {
        authenticated: false,
        error: (medusaError as Error).message
      };
    }

    // Prepare cookie data with user information and token
    const cookieData = {
      isAuthenticated: true,
      userId: userId || '',
      username: username_from_token || username,
      email: username.includes('@') ? username : (userAttributes as any)?.email || '',
      name: (userAttributes as any)?.name || username_from_token || username,
      accessToken: accessToken,
      expiration: expiresIn ? Date.now() + (expiresIn * 1000) : undefined,
      medusa: {
        authenticated: medusaResult.authenticated,
        customer: medusaResult.customer,
        token: medusaToken
      }
    };

    // Set authentication cookie using our enhanced function
    setAuthCookie(event, AUTH_COOKIE_NAME, JSON.stringify(cookieData));
    console.log('Auth cookie set successfully in handleSignIn');

    // Return a structured response with the user data
    return {
      success: true,
      user: {
        userId: userId || '',
        username: username_from_token || username,
        email: username.includes('@') ? username : (userAttributes as any)?.email || '',
        name: (userAttributes as any)?.name || username_from_token || username,
        medusa: {
          authenticated: medusaResult.authenticated,
          customer: medusaResult.customer,
          token: medusaToken
        }
      },
      message: 'Authentication successful'
    };
  } catch (error) {
    console.error('Sign in error:', error);
    
    // Get the username from the body again
    const body = await readBody(event);
    const username = body.username || '';
    
    if ((error as any).name === 'UserNotConfirmedException') {
      return {
        success: false,
        error: 'User is not confirmed',
        nextChallenge: {
          challengeName: 'USER_NOT_CONFIRMED',
          username: username
        }
      };
    }
    
    if ((error as any).name === 'NotAuthorizedException') {
      throw createError({
        statusCode: 401,
        statusMessage: 'Incorrect username or password'
      });
    }
    
    if ((error as any).name === 'UserNotFoundException') {
      throw createError({
        statusCode: 401,
        statusMessage: 'Incorrect username or password'
      });
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: (error as Error).message
    });
  }
};

const handleConfirmSignUp = async (event: H3Event) => {
  const body = await readBody(event);
  const { username, confirmationCode } = body;

  // Make sure we have the Cognito configuration
  const config = useRuntimeConfig();
  if (!config.cognitoUserPoolId || !config.cognitoClientId || !config.cognitoClientSecret) {
    throw createError({
      statusCode: 400,
      message: "Auth UserPool not configured. Missing Cognito configuration.",
    });
  }

  // The username passed here should be the generated uniqueUsername from signup
  // If it looks like an email, we need to throw an error since we expect the generated username
  if (username.includes('@')) {
    throw createError({
      statusCode: 400,
      message: "Please use the generated username returned from signup, not the email address.",
    });
  }

  // Generate the SECRET_HASH using our new function
  const secretHash = calculateSecretHash(
    username, 
    config.cognitoClientId, 
    config.cognitoClientSecret
  );

  try {
    // Use the AWS SDK directly to handle the SECRET_HASH
    const client = new CognitoIdentityProviderClient({
      region: config.cognitoRegion,
    });

    // Create the confirm sign-up command with the SECRET_HASH
    const confirmCommand = new ConfirmSignUpCommand({
      ClientId: config.cognitoClientId,
      Username: username,
      ConfirmationCode: confirmationCode,
      SecretHash: secretHash,
    });

    // Execute the confirm sign-up command
    await client.send(confirmCommand);

    // Try to sync the customer with Medusa
    try {
      // Implementation for Medusa sync if needed
      // For now, we'll just return success without Medusa sync
    } catch (syncError) {
      console.error("Failed to sync with Medusa after confirmation:", syncError);
      // We don't throw here because the sign-up is confirmed, even if sync fails
    }

    return {
      isSignUpComplete: true,
      nextStep: {
        signUpStep: 'DONE',
      },
    };
  } catch (error: any) {
    console.error("Confirm signup error:", error);
    throw createError({
      statusCode: 400,
      message: error.message,
    });
  }
};

const handleConfirmResetPassword = async (event: H3Event) => {
  const body = await readBody(event);
  const { username, code, newPassword, name } = body;
  const secretHash = generateSecretHash(username);

  try {
    // 1. Reset password in Cognito
    const cognitoResult = await confirmResetPasswordCognito(username, code, newPassword);
    
    // 2. If confirmation is successful, create a Medusa customer
    const medusaClient = getMedusaClient();
    
    // First check if customer exists
    try {
      await medusaClient.auth.authenticate({
        email: username,
        password: newPassword
      });
    } catch (authError) {
      // Parse name into first and last name if available
      let firstName = '';
      let lastName = '';
      
      if (name) {
        const nameParts = name.split(' ');
        firstName = nameParts[0] || '';
        lastName = nameParts.slice(1).join(' ') || '';
      }
      
      // If customer doesn't exist, create one - ensure required fields are included
      await medusaClient.customers.create({
        email: username,
        password: newPassword,
        first_name: firstName, 
        last_name: lastName
      });
    }
    
    return cognitoResult;
  } catch (error: any) {
    throw createError({
      statusCode: 400,
      message: error.message,
    });
  }
};

const handleResetPassword = async (event: H3Event) => {
  const body = await readBody(event);
  const { username } = body;
  const secretHash = generateSecretHash(username);

  try {
    const result = await resetPasswordCognito(username);
    
    return result;
  } catch (error: any) {
    throw createError({
      statusCode: 400,
      message: error.message,
    });
  }
};

// Define cookie settings consistently with improved options for persistence
const COOKIE_SETTINGS = {
  maxAge: COOKIE_OPTIONS.maxAge,
  path: COOKIE_OPTIONS.path,
  secure: COOKIE_OPTIONS.secure,
  httpOnly: false, // Must be false to be accessible by JavaScript
  sameSite: COOKIE_OPTIONS.sameSite as 'lax' | 'strict' | 'none' | undefined,
  // Don't use the expires option - it can cause issues with cookie expiration
  // Don't use domain - let the browser handle it
};

// Debug function to log cookie operations
const logCookieOperation = (operation: string, name: string, value: any) => {
  console.log(`Cookie ${operation}: ${name}, size: ${
    typeof value === 'string' ? value.length : 'N/A'
  } bytes, options: ${JSON.stringify(COOKIE_SETTINGS)}`);
}

// Enhanced setCookie wrapper for debug logging
const setAuthCookie = (event: H3Event, name: string, value: string) => {
  logCookieOperation('set', name, value);
  setCookie(event, name, value, COOKIE_SETTINGS);
}

const handleGetCurrentUser = async (event: H3Event) => {
  try {
    // Get Cognito user with access token from cookie
    const accessToken = await getAccessToken(event);
    
    // If no access token, user is not authenticated
    if (!accessToken) {
      console.log('No access token found in handleGetCurrentUser');
      return {
        isAuthenticated: false,
        message: 'No access token found'
      };
    }
    
    console.log('Access token found in handleGetCurrentUser, length:', accessToken.length);
    
    // Get Cognito user with the access token
    const cognitoUser = await getCurrentUserFromCognito(accessToken);
    
    // Try to get Medusa customer
    let medusaCustomer = null;
    let medusaAuthenticated = false;
    
    try {
      const medusa = getMedusaClient();
      const medusaResponse = await medusa.customers.retrieve();
      
      if (medusaResponse.customer) {
        medusaCustomer = medusaResponse.customer;
        medusaAuthenticated = true;
      }
    } catch (medusaError) {
      console.log('Medusa customer retrieval error:', medusaError);
      // Continue without Medusa data
    }
    
    // Create response with complete user data
    const userData = {
      isAuthenticated: true,
      userId: cognitoUser.userId,
      username: cognitoUser.username,
      email: cognitoUser.signInDetails?.loginId || cognitoUser.attributes?.email,
      name: cognitoUser.attributes?.name || cognitoUser.username,
      attributes: cognitoUser.attributes || {},
      medusa: {
        customer: medusaCustomer,
        authenticated: medusaAuthenticated
      }
    };
    
    // Create auth cookie data with complete user info
    const cookieData = {
      isAuthenticated: true,
      userId: cognitoUser.userId,
      username: cognitoUser.username,
      email: cognitoUser.signInDetails?.loginId || cognitoUser.attributes?.email,
      name: cognitoUser.attributes?.name || cognitoUser.username,
      accessToken: accessToken
    };
    
    // Set auth cookie with updated user data
    setAuthCookie(event, AUTH_COOKIE_NAME, JSON.stringify(cookieData));
    console.log('Auth cookie set in handleGetCurrentUser');
    
    return userData;
  } catch (error) {
    console.error('Get current user error:', error);
    
    // Return unauthenticated response
    return {
      isAuthenticated: false,
      error: (error as Error).message
    };
  }
};

// Helper function to properly clear cookies
const clearAuthCookie = (event: H3Event, name: string) => {
  logCookieOperation('clear', name, null);
  
  // Clear using Nuxt's setCookie with null value
  setCookie(event, name, '', {
    maxAge: -1,
    path: '/',
    httpOnly: false,
    secure: COOKIE_OPTIONS.secure,
    sameSite: COOKIE_OPTIONS.sameSite as 'lax' | 'strict' | 'none' | undefined
  });
  
  // Also set header directly to ensure the cookie is cleared
  appendHeader(event, 'Set-Cookie', `${name}=; Max-Age=0; Path=/; SameSite=Lax; ${COOKIE_OPTIONS.secure ? 'Secure;' : ''}`);
  
  console.log('Auth cookie cleared');
}

const handleSignOut = async (event: H3Event) => {
  try {
    // Get the access token
    const accessToken = await getAccessToken(event);
    
    if (accessToken) {
      try {
        // Use the Cognito SDK to sign out the user
        const signOutCommand = new GlobalSignOutCommand({
          AccessToken: accessToken
        });
        
        await cognitoClient.send(signOutCommand);
        console.log('Global sign-out command sent to Cognito');
      } catch (signOutError) {
        // Log but don't fail if Cognito sign out fails
        console.error('Error during Cognito sign out:', signOutError);
      }
    } else {
      console.log('No access token found during sign out');
    }

    // Clear the auth cookie
    clearAuthCookie(event, AUTH_COOKIE_NAME);
    
    // Attempt to clear Medusa customer state if needed
    try {
      const medusa = getMedusaClient();
      await medusa.auth.deleteSession();
      console.log('Medusa session deleted');
    } catch (medusaError) {
      // Don't fail if Medusa logout fails
      console.error('Error clearing Medusa session:', medusaError);
    }

    return {
      success: true,
      message: 'Signed out successfully'
    };
  } catch (error) {
    console.error('Error during sign out:', error);
    
    // Always clear cookies even if there was an error
    clearAuthCookie(event, AUTH_COOKIE_NAME);
    
    return {
      success: false,
      message: 'An error occurred during sign out, but cookies were cleared'
    };
  }
};

const handleResendCode = async (event: H3Event) => {
  const body = await readBody(event);
  let { username } = body;

  // Make sure we have the Cognito configuration
  const config = useRuntimeConfig();
  if (!config.cognitoUserPoolId || !config.cognitoClientId || !config.cognitoClientSecret) {
    throw createError({
      statusCode: 400,
      message: "Auth UserPool not configured. Missing Cognito configuration.",
    });
  }

  // If username looks like an email, we need to find the actual username
  if (username.includes('@')) {
    const actualUsername = await findUserByEmail(username);
    if (!actualUsername) {
      throw createError({
        statusCode: 400,
        message: "User not found with this email address.",
      });
    }
    username = actualUsername;
  }

  // Generate the SECRET_HASH using our new function
  const secretHash = calculateSecretHash(
    username,
    config.cognitoClientId,
    config.cognitoClientSecret
  );

  try {
    // Use the AWS SDK directly to handle the SECRET_HASH
    const client = new CognitoIdentityProviderClient({
      region: config.cognitoRegion,
    });

    // Create the resend confirmation code command
    const resendCommand = new ResendConfirmationCodeCommand({
      ClientId: config.cognitoClientId,
      Username: username,
      SecretHash: secretHash,
    });

    // Execute the command
    await client.send(resendCommand);

    return {
      success: true,
      message: "Confirmation code resent successfully.",
    };
  } catch (error: any) {
    console.error("Resend code error:", error);
    throw createError({
      statusCode: 400,
      message: error.message,
    });
  }
};

const handleUpdateUser = async (event: H3Event) => {
  try {
    const { name, phoneNumber } = await readBody(event);

    if (!name && !phoneNumber) {
      throw createError({
        statusCode: 400,
        message: 'At least one attribute to update is required',
      });
    }

    // Get current user's access token
    const accessToken = await getAccessToken(event);
    if (!accessToken) {
      throw createError({
        statusCode: 401,
        message: 'No access token available, please sign in again',
      });
    }

    // Build attributes object with the attributes we want to update
    const attributes: Record<string, string> = {};
    if (name) attributes.name = name;
    if (phoneNumber) attributes.phone_number = phoneNumber;

    // 1. Update Cognito user attributes
    await updateUserAttributesCognito(accessToken, attributes);
      
    // 2. Get updated user data
    const updatedUser = await getCurrentUserFromCognito(accessToken);

    return {
      success: true,
      user: updatedUser
    };
  } catch (error: any) {
    console.error('Error updating user:', error);
    throw createError({
      statusCode: 500,
      message: error.message || 'An error occurred while updating user attributes',
    });
  }
};

// Since changePassword is not available directly in aws-amplify/auth, we'll implement a workaround
const handleChangePassword = async (event: H3Event) => {
  const body = await readBody(event);
  const { oldPassword, newPassword } = body;
  
  try {
    // 1. Since changePassword is not directly available, we'll use updateUser approach
    // First authenticate the current user with the old password
    const cognitoUser = await getCurrentUserFromCognito();
    
    // We cannot use changePassword directly, so we'll handle Cognito password change manually
    // You may need to implement this using the Cognito SDK or API directly
    
    // For now, notify the user that Cognito password change is not supported in this version
    console.warn("Cognito direct password change is not supported in this version of aws-amplify/auth");
    const cognitoUpdated = false;
    
    // 2. Try to change password in Medusa
    try {
      const medusa = getMedusaClient();
      
      // Get current user email
      const email = cognitoUser.signInDetails?.loginId;
      
      if (email) {
        // First authenticate with old password
        await medusa.auth.authenticate({
          email,
          password: oldPassword
        });
        
        // Then update password
        const medusaResult = await medusa.customers.update({
          password: newPassword
        });
        
        return {
          success: true,
          cognitoUpdated: cognitoUpdated,
          cognitoMessage: "Cognito password change is not supported directly in this version",
          medusa: {
            updated: true
          }
        };
      } else {
        return {
          success: false,
          cognitoUpdated: cognitoUpdated,
          cognitoMessage: "Cognito password change is not supported directly in this version",
          medusa: {
            updated: false,
            error: "Email not found in Cognito user"
          }
        };
      }
    } catch (medusaError) {
      // Return success even if Medusa update fails
      const typedError = medusaError as Error;
      return {
        success: false,
        cognitoUpdated: cognitoUpdated,
        cognitoMessage: "Cognito password change is not supported directly in this version",
        medusa: {
          updated: false,
          error: typedError.message
        }
      };
    }
  } catch (error: any) {
    throw createError({
      statusCode: 400,
      message: error.message,
    });
  }
};

// Add a server-side implementation of getCurrentUser
// This function replaces the aws-amplify/auth getCurrentUser
const getCurrentUserFromCognito = async (accessToken?: string): Promise<any> => {
  try {
    // If no access token is provided, try to get it
    if (!accessToken) {
      accessToken = await getAccessToken();
    }
    
    if (!accessToken) {
      throw new Error('No access token available');
    }

    // Use the AWS SDK to get user details with the access token
    const getUserCommand = new GetUserCommand({
      AccessToken: accessToken
    });

    const userResult = await cognitoClient.send(getUserCommand);
    
    // Map the result to match what the frontend expects
    if (userResult && userResult.Username) {
      // Transform the user attributes array into an object
      const attributes: Record<string, string> = {};
      if (userResult.UserAttributes) {
        userResult.UserAttributes.forEach(attr => {
          if (attr.Name && attr.Value) {
            attributes[attr.Name] = attr.Value;
          }
        });
      }

      return {
        userId: attributes['sub'] || '',
        username: userResult.Username,
        signInDetails: {
          loginId: attributes['email'] || userResult.Username
        },
        attributes: attributes
      };
    }
    
    throw new Error('User not found');
  } catch (error) {
    console.error('Error getting current user:', error);
    throw error;
  }
};

// Add a serverside implementation of updateUserAttributes
const updateUserAttributesCognito = async (accessToken: string, attributes: Record<string, string>): Promise<any> => {
  try {
    if (!accessToken) {
      throw new Error('No access token available');
    }
    
    // Convert attributes object to an array of AttributeType
    const attributesList = Object.entries(attributes).map(([Name, Value]) => ({
      Name,
      Value
    }));
    
    // Use the AWS SDK to update user attributes
    const updateCommand = {
      AccessToken: accessToken,
      UserAttributes: attributesList
    };
    
    // Need to import the command first
    const { UpdateUserAttributesCommand } = await import('@aws-sdk/client-cognito-identity-provider');
    const command = new UpdateUserAttributesCommand(updateCommand);
    
    const result = await cognitoClient.send(command);
    return result;
  } catch (error) {
    console.error('Error updating user attributes:', error);
    throw error;
  }
};

// Add a server-side implementation of resetPassword
const resetPasswordCognito = async (username: string): Promise<any> => {
  try {
    const config = useRuntimeConfig();
    const clientId = config.public.cognitoClientId || process.env.COGNITO_CLIENT_ID;
    
    if (!clientId) {
      throw new Error('Missing Cognito client ID');
    }
    
    const secretHash = generateSecretHash(username);
    
    // Use the ForgotPasswordCommand to initiate password reset
    const forgotPasswordCommand = new ForgotPasswordCommand({
      ClientId: clientId,
      Username: username,
      SecretHash: secretHash
    });
    
    const result = await cognitoClient.send(forgotPasswordCommand);
    return result;
  } catch (error) {
    console.error('Error initiating password reset:', error);
    throw error;
  }
};

// Add a server-side implementation of confirmResetPassword
const confirmResetPasswordCognito = async (username: string, code: string, newPassword: string): Promise<any> => {
  try {
    const config = useRuntimeConfig();
    const clientId = config.public.cognitoClientId || process.env.COGNITO_CLIENT_ID;
    
    if (!clientId) {
      throw new Error('Missing Cognito client ID');
    }
    
    const secretHash = generateSecretHash(username);
    
    // Use the ConfirmForgotPasswordCommand to confirm password reset
    const confirmForgotPasswordCommand = new ConfirmForgotPasswordCommand({
      ClientId: clientId,
      Username: username,
      ConfirmationCode: code,
      Password: newPassword,
      SecretHash: secretHash
    });
    
    const result = await cognitoClient.send(confirmForgotPasswordCommand);
    return result;
  } catch (error) {
    console.error('Error confirming password reset:', error);
    throw error;
  }
};

export default defineEventHandler(async (event) => {
  const action = getRouterParam(event, '_');

  switch (action) {
    case 'signup': {
      const result = await handleSignUp(event);
      // Store the generated username in the session for confirmation
      event.context.session = event.context.session || {};
      event.context.session.generatedUsername = result.username;
      event.context.session.userEmail = result.email;
      return result;
    }
    case 'signin':
      return handleSignIn(event);
    case 'confirm-signup': {
      const body = await readBody(event);
      // If there's a generated username in the session, use it
      if (event.context.session?.generatedUsername && !body.username) {
        body.username = event.context.session.generatedUsername;
      }
      return handleConfirmSignUp(event);
    }
    case 'reset':
      // Handle the 'reset' endpoint the same as 'reset-password'
      return handleResetPassword(event);
    case 'reset-password':
      return handleResetPassword(event);
    case 'confirm-reset-password':
      return handleConfirmResetPassword(event);
    case 'current-user':
    case 'me':
      return handleGetCurrentUser(event);
    case 'signout':
      return handleSignOut(event);
    case 'resend-code':
      return handleResendCode(event);
    case 'confirm':
      return handleConfirmSignUp(event); // Reuse confirm-signup handler
    case 'update-user':
      return handleUpdateUser(event);
    case 'change-password':
      return handleChangePassword(event);
    default:
      throw createError({
        statusCode: 404,
        message: 'Not found',
      });
  }
});
