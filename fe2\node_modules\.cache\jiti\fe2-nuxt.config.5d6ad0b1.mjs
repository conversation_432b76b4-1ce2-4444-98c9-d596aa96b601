"use strict";Object.defineProperty(exports, "__esModule", { value: true });exports.default = void 0;
var _url = await jitiImport("url");
var _path = await jitiImport("path"); // https://nuxt.com/docs/api/configuration/nuxt-config

const _dirname = (0, _path.dirname)((0, _url.fileURLToPath)("file:///C:/Users/<USER>/PhpstormProjects/ro/fe2/nuxt.config.ts"));var _default = exports.default =

defineNuxtConfig({
  // Disable SSR to avoid Vue app alias issues in server runtime
  ssr: true,
  devtools: { enabled: true },

  nitro: {
    externals: {
      inline: ['tslib'] // Bundle tslib into the output
    }
  },

  modules: [
  '@nuxt/image',
  '@vueuse/nuxt',
  '@pinia/nuxt',
  '@nuxt/ui',
  '@nuxtjs/i18n',
  'nuxt-gtag'],


  gtag: {
    id: 'AW-16546086564', // Google Ads tag ID
    config: {
      cookie_prefix: '_ga',
      cookie_domain: 'handmadein.ro',
      cookie_expires: 63072000 // 2 years in seconds
    }
  },

  // Server configuration
  devServer: {
    port: 3001,
    host: 'localhost'
  },

  // Runtime config
  runtimeConfig: {
    public: {
      serverUrl: process.env.SERVER_URL || 'http://localhost:3001',
      // Use Cloudflare backend as the primary API
      apiUrl: process.env.CLOUDFLARE_API_URL || 'http://localhost:8787',
      cloudflareApiUrl: process.env.CLOUDFLARE_API_URL || 'http://localhost:8787',
      stripePublishableKey: process.env.STRIPE_PUBLISHABLE_KEY || 'pk_live_51OPQ1JFoCiddKlDTzrXnKLKPqoaFoTLf50HRafJOua0Kwop37RblK6pE6ekhAQDprLPwNgULef96p1ZefA41hE4100N552Mvnm',
      reviewsApiEndpoint: process.env.REVIEWS_API_ENDPOINT || 'https://reviews.handmadein.ro',
      defaultCountry: 'RO',
      defaultLanguage: 'ro'
    }
  },

  css: ['~/assets/css/main.css'],

  plugins: [
  '~/plugins/i18n.ts',
  '~/plugins/components.ts',
  '~/plugins/locale-persist.client.ts',
  '~/plugins/i18n-route-sync.client.ts',
  '~/plugins/i18n-cookie-consent.client.ts',
  '~/plugins/cookie-manager.client.ts',
  '~/plugins/trusted-shop.client.ts'],


  build: {
    transpile: []
  },

  vite: {
    define: {
      'window.global': 'window'
    },
    optimizeDeps: {
      esbuildOptions: {
        target: 'es2020'
      }
    },
    build: {
      target: 'es2020'
    }
  },

  ui: {
    // @ts-ignore
    icons: ['heroicons', 'simple-icons']
  },

  i18n: {
    baseUrl: 'https://handmadein.ro',
    defaultLocale: 'ro',
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'i18n_redirected',
      redirectOn: 'root',
      cookieSecure: process.env.NODE_ENV === 'production',
      cookieCrossOrigin: true,
      alwaysRedirect: false
    },
    strategy: 'no_prefix',
    langDir: 'locales',
    locales: [
    {
      code: 'ro',
      name: 'Română',
      iso: 'ro-RO',
      file: 'ro.json'
    }],

    lazy: true,
    vueI18n: './i18n.config.ts',
    skipSettingLocaleOnNavigate: false
  },

  compatibilityDate: '2025-02-24'
}); /* v9-582f03d0207332f3 */
