// Generated by auto imports
export {}
declare global {
  const _useShortcuts: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useShortcuts')['_useShortcuts']
  const abortNavigation: typeof import('../../node_modules/nuxt/dist/app/composables/router')['abortNavigation']
  const addRouteMiddleware: typeof import('../../node_modules/nuxt/dist/app/composables/router')['addRouteMiddleware']
  const asyncComputed: typeof import('@vueuse/core')['asyncComputed']
  const autoResetRef: typeof import('@vueuse/core')['autoResetRef']
  const callOnce: typeof import('../../node_modules/nuxt/dist/app/composables/once')['callOnce']
  const cancelIdleCallback: typeof import('../../node_modules/nuxt/dist/app/compat/idle-callback')['cancelIdleCallback']
  const clearError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['clearError']
  const clearNuxtData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['clearNuxtData']
  const clearNuxtState: typeof import('../../node_modules/nuxt/dist/app/composables/state')['clearNuxtState']
  const cloudflareApiFetch: typeof import('../../utils/api')['cloudflareApiFetch']
  const computed: typeof import('../../node_modules/vue')['computed']
  const computedAsync: typeof import('@vueuse/core')['computedAsync']
  const computedEager: typeof import('@vueuse/core')['computedEager']
  const computedInject: typeof import('@vueuse/core')['computedInject']
  const computedWithControl: typeof import('@vueuse/core')['computedWithControl']
  const controlledComputed: typeof import('@vueuse/core')['controlledComputed']
  const controlledRef: typeof import('@vueuse/core')['controlledRef']
  const createError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['createError']
  const createEventHook: typeof import('@vueuse/core')['createEventHook']
  const createGenericProjection: typeof import('../../node_modules/@vueuse/math')['createGenericProjection']
  const createGlobalState: typeof import('@vueuse/core')['createGlobalState']
  const createInjectionState: typeof import('@vueuse/core')['createInjectionState']
  const createPopper: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/usePopper')['createPopper']
  const createProjection: typeof import('../../node_modules/@vueuse/math')['createProjection']
  const createReactiveFn: typeof import('@vueuse/core')['createReactiveFn']
  const createReusableTemplate: typeof import('@vueuse/core')['createReusableTemplate']
  const createSharedComposable: typeof import('@vueuse/core')['createSharedComposable']
  const createTemplatePromise: typeof import('@vueuse/core')['createTemplatePromise']
  const createUnrefFn: typeof import('@vueuse/core')['createUnrefFn']
  const customRef: typeof import('../../node_modules/vue')['customRef']
  const debouncedRef: typeof import('@vueuse/core')['debouncedRef']
  const debouncedWatch: typeof import('@vueuse/core')['debouncedWatch']
  const defineAppConfig: typeof import('../../node_modules/nuxt/dist/app/nuxt')['defineAppConfig']
  const defineAsyncComponent: typeof import('../../node_modules/vue')['defineAsyncComponent']
  const defineComponent: typeof import('../../node_modules/vue')['defineComponent']
  const defineI18nConfig: typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['defineI18nConfig']
  const defineI18nLocale: typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['defineI18nLocale']
  const defineI18nRoute: typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['defineI18nRoute']
  const defineNuxtComponent: typeof import('../../node_modules/nuxt/dist/app/composables/component')['defineNuxtComponent']
  const defineNuxtLink: typeof import('../../node_modules/nuxt/dist/app/components/nuxt-link')['defineNuxtLink']
  const defineNuxtPlugin: typeof import('../../node_modules/nuxt/dist/app/nuxt')['defineNuxtPlugin']
  const defineNuxtRouteMiddleware: typeof import('../../node_modules/nuxt/dist/app/composables/router')['defineNuxtRouteMiddleware']
  const definePageMeta: typeof import('../../node_modules/nuxt/dist/pages/runtime/composables')['definePageMeta']
  const definePayloadPlugin: typeof import('../../node_modules/nuxt/dist/app/nuxt')['definePayloadPlugin']
  const definePayloadReducer: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['definePayloadReducer']
  const definePayloadReviver: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['definePayloadReviver']
  const defineShortcuts: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/defineShortcuts')['defineShortcuts']
  const eagerComputed: typeof import('@vueuse/core')['eagerComputed']
  const effect: typeof import('../../node_modules/vue')['effect']
  const effectScope: typeof import('../../node_modules/vue')['effectScope']
  const extendRef: typeof import('@vueuse/core')['extendRef']
  const fetchPricesById: typeof import('../../utils/price')['fetchPricesById']
  const fetchSinglePrice: typeof import('../../utils/price')['fetchSinglePrice']
  const getAppManifest: typeof import('../../node_modules/nuxt/dist/app/composables/manifest')['getAppManifest']
  const getCurrentInstance: typeof import('../../node_modules/vue')['getCurrentInstance']
  const getCurrentScope: typeof import('../../node_modules/vue')['getCurrentScope']
  const getRouteRules: typeof import('../../node_modules/nuxt/dist/app/composables/manifest')['getRouteRules']
  const getServerSession: typeof import('../../composables/useAuth')['getServerSession']
  const h: typeof import('../../node_modules/vue')['h']
  const hasInjectionContext: typeof import('../../node_modules/vue')['hasInjectionContext']
  const ignorableWatch: typeof import('@vueuse/core')['ignorableWatch']
  const inject: typeof import('../../node_modules/vue')['inject']
  const injectHead: typeof import('../../node_modules/@unhead/vue')['injectHead']
  const injectLocal: typeof import('@vueuse/core')['injectLocal']
  const isDefined: typeof import('@vueuse/core')['isDefined']
  const isNuxtError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['isNuxtError']
  const isPrerendered: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['isPrerendered']
  const isProxy: typeof import('../../node_modules/vue')['isProxy']
  const isReactive: typeof import('../../node_modules/vue')['isReactive']
  const isReadonly: typeof import('../../node_modules/vue')['isReadonly']
  const isRef: typeof import('../../node_modules/vue')['isRef']
  const isShallow: typeof import('../../node_modules/vue')['isShallow']
  const isVue2: typeof import('../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue2']
  const isVue3: typeof import('../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue3']
  const loadPayload: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['loadPayload']
  const logicAnd: typeof import('../../node_modules/@vueuse/math')['logicAnd']
  const logicNot: typeof import('../../node_modules/@vueuse/math')['logicNot']
  const logicOr: typeof import('../../node_modules/@vueuse/math')['logicOr']
  const makeDestructurable: typeof import('@vueuse/core')['makeDestructurable']
  const markRaw: typeof import('../../node_modules/vue')['markRaw']
  const mergeModels: typeof import('../../node_modules/vue')['mergeModels']
  const modalInjectionKey: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useModal')['modalInjectionKey']
  const navigateTo: typeof import('../../node_modules/nuxt/dist/app/composables/router')['navigateTo']
  const nextTick: typeof import('../../node_modules/vue')['nextTick']
  const onActivated: typeof import('../../node_modules/vue')['onActivated']
  const onBeforeMount: typeof import('../../node_modules/vue')['onBeforeMount']
  const onBeforeRouteLeave: typeof import('../../node_modules/vue-router')['onBeforeRouteLeave']
  const onBeforeRouteUpdate: typeof import('../../node_modules/vue-router')['onBeforeRouteUpdate']
  const onBeforeUnmount: typeof import('../../node_modules/vue')['onBeforeUnmount']
  const onBeforeUpdate: typeof import('../../node_modules/vue')['onBeforeUpdate']
  const onClickOutside: typeof import('@vueuse/core')['onClickOutside']
  const onDeactivated: typeof import('../../node_modules/vue')['onDeactivated']
  const onElementRemoval: typeof import('@vueuse/core')['onElementRemoval']
  const onErrorCaptured: typeof import('../../node_modules/vue')['onErrorCaptured']
  const onKeyStroke: typeof import('@vueuse/core')['onKeyStroke']
  const onLongPress: typeof import('@vueuse/core')['onLongPress']
  const onMounted: typeof import('../../node_modules/vue')['onMounted']
  const onNuxtReady: typeof import('../../node_modules/nuxt/dist/app/composables/ready')['onNuxtReady']
  const onPrehydrate: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['onPrehydrate']
  const onRenderTracked: typeof import('../../node_modules/vue')['onRenderTracked']
  const onRenderTriggered: typeof import('../../node_modules/vue')['onRenderTriggered']
  const onScopeDispose: typeof import('../../node_modules/vue')['onScopeDispose']
  const onServerPrefetch: typeof import('../../node_modules/vue')['onServerPrefetch']
  const onStartTyping: typeof import('@vueuse/core')['onStartTyping']
  const onUnmounted: typeof import('../../node_modules/vue')['onUnmounted']
  const onUpdated: typeof import('../../node_modules/vue')['onUpdated']
  const pausableWatch: typeof import('@vueuse/core')['pausableWatch']
  const prefetchComponents: typeof import('../../node_modules/nuxt/dist/app/composables/preload')['prefetchComponents']
  const preloadComponents: typeof import('../../node_modules/nuxt/dist/app/composables/preload')['preloadComponents']
  const preloadPayload: typeof import('../../node_modules/nuxt/dist/app/composables/payload')['preloadPayload']
  const preloadRouteComponents: typeof import('../../node_modules/nuxt/dist/app/composables/preload')['preloadRouteComponents']
  const prerenderRoutes: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['prerenderRoutes']
  const provide: typeof import('../../node_modules/vue')['provide']
  const provideLocal: typeof import('@vueuse/core')['provideLocal']
  const proxyRefs: typeof import('../../node_modules/vue')['proxyRefs']
  const reactify: typeof import('@vueuse/core')['reactify']
  const reactifyObject: typeof import('@vueuse/core')['reactifyObject']
  const reactive: typeof import('../../node_modules/vue')['reactive']
  const reactiveComputed: typeof import('@vueuse/core')['reactiveComputed']
  const reactiveOmit: typeof import('@vueuse/core')['reactiveOmit']
  const reactivePick: typeof import('@vueuse/core')['reactivePick']
  const readonly: typeof import('../../node_modules/vue')['readonly']
  const ref: typeof import('../../node_modules/vue')['ref']
  const refAutoReset: typeof import('@vueuse/core')['refAutoReset']
  const refDebounced: typeof import('@vueuse/core')['refDebounced']
  const refDefault: typeof import('@vueuse/core')['refDefault']
  const refThrottled: typeof import('@vueuse/core')['refThrottled']
  const refWithControl: typeof import('@vueuse/core')['refWithControl']
  const refreshCookie: typeof import('../../node_modules/nuxt/dist/app/composables/cookie')['refreshCookie']
  const refreshNuxtData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['refreshNuxtData']
  const reloadNuxtApp: typeof import('../../node_modules/nuxt/dist/app/composables/chunk')['reloadNuxtApp']
  const requestIdleCallback: typeof import('../../node_modules/nuxt/dist/app/compat/idle-callback')['requestIdleCallback']
  const resolveComponent: typeof import('../../node_modules/vue')['resolveComponent']
  const resolveRef: typeof import('@vueuse/core')['resolveRef']
  const resolveUnref: typeof import('@vueuse/core')['resolveUnref']
  const setInterval: typeof import('../../node_modules/nuxt/dist/app/compat/interval')['setInterval']
  const setPageLayout: typeof import('../../node_modules/nuxt/dist/app/composables/router')['setPageLayout']
  const setResponseStatus: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['setResponseStatus']
  const shallowReactive: typeof import('../../node_modules/vue')['shallowReactive']
  const shallowReadonly: typeof import('../../node_modules/vue')['shallowReadonly']
  const shallowRef: typeof import('../../node_modules/vue')['shallowRef']
  const showError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['showError']
  const slidOverInjectionKey: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useSlideover')['slidOverInjectionKey']
  const syncRef: typeof import('@vueuse/core')['syncRef']
  const syncRefs: typeof import('@vueuse/core')['syncRefs']
  const templateRef: typeof import('@vueuse/core')['templateRef']
  const throttledRef: typeof import('@vueuse/core')['throttledRef']
  const throttledWatch: typeof import('@vueuse/core')['throttledWatch']
  const toRaw: typeof import('../../node_modules/vue')['toRaw']
  const toReactive: typeof import('@vueuse/core')['toReactive']
  const toRef: typeof import('../../node_modules/vue')['toRef']
  const toRefs: typeof import('../../node_modules/vue')['toRefs']
  const toValue: typeof import('../../node_modules/vue')['toValue']
  const triggerRef: typeof import('../../node_modules/vue')['triggerRef']
  const tryOnBeforeMount: typeof import('@vueuse/core')['tryOnBeforeMount']
  const tryOnBeforeUnmount: typeof import('@vueuse/core')['tryOnBeforeUnmount']
  const tryOnMounted: typeof import('@vueuse/core')['tryOnMounted']
  const tryOnScopeDispose: typeof import('@vueuse/core')['tryOnScopeDispose']
  const tryOnUnmounted: typeof import('@vueuse/core')['tryOnUnmounted']
  const tryUseNuxtApp: typeof import('../../node_modules/nuxt/dist/app/nuxt')['tryUseNuxtApp']
  const unref: typeof import('../../node_modules/vue')['unref']
  const unrefElement: typeof import('@vueuse/core')['unrefElement']
  const until: typeof import('@vueuse/core')['until']
  const updateAppConfig: typeof import('../../node_modules/nuxt/dist/app/config')['updateAppConfig']
  const useAbs: typeof import('../../node_modules/@vueuse/math')['useAbs']
  const useActiveElement: typeof import('@vueuse/core')['useActiveElement']
  const useAnimate: typeof import('@vueuse/core')['useAnimate']
  const useAppConfig: typeof import('../../node_modules/nuxt/dist/app/config')['useAppConfig']
  const useArrayDifference: typeof import('@vueuse/core')['useArrayDifference']
  const useArrayEvery: typeof import('@vueuse/core')['useArrayEvery']
  const useArrayFilter: typeof import('@vueuse/core')['useArrayFilter']
  const useArrayFind: typeof import('@vueuse/core')['useArrayFind']
  const useArrayFindIndex: typeof import('@vueuse/core')['useArrayFindIndex']
  const useArrayFindLast: typeof import('@vueuse/core')['useArrayFindLast']
  const useArrayIncludes: typeof import('@vueuse/core')['useArrayIncludes']
  const useArrayJoin: typeof import('@vueuse/core')['useArrayJoin']
  const useArrayMap: typeof import('@vueuse/core')['useArrayMap']
  const useArrayReduce: typeof import('@vueuse/core')['useArrayReduce']
  const useArraySome: typeof import('@vueuse/core')['useArraySome']
  const useArrayUnique: typeof import('@vueuse/core')['useArrayUnique']
  const useAsyncData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useAsyncData']
  const useAsyncQueue: typeof import('@vueuse/core')['useAsyncQueue']
  const useAsyncState: typeof import('@vueuse/core')['useAsyncState']
  const useAttrs: typeof import('../../node_modules/vue')['useAttrs']
  const useAuth: typeof import('../../composables/useAuth')['useAuth']
  const useAverage: typeof import('../../node_modules/@vueuse/math')['useAverage']
  const useBase64: typeof import('@vueuse/core')['useBase64']
  const useBattery: typeof import('@vueuse/core')['useBattery']
  const useBluetooth: typeof import('@vueuse/core')['useBluetooth']
  const useBreakpoints: typeof import('@vueuse/core')['useBreakpoints']
  const useBroadcastChannel: typeof import('@vueuse/core')['useBroadcastChannel']
  const useBrowserLocale: typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useBrowserLocale']
  const useBrowserLocation: typeof import('@vueuse/core')['useBrowserLocation']
  const useCached: typeof import('@vueuse/core')['useCached']
  const useCarouselScroll: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useCarouselScroll')['useCarouselScroll']
  const useCart: typeof import('../../composables/useCart')['useCart']
  const useCartContext: typeof import('../../composables/useCartContext')['useCartContext']
  const useCartContextInject: typeof import('../../composables/useCartContext')['useCartContextInject']
  const useCartContextProvider: typeof import('../../composables/useCartContext')['useCartContextProvider']
  const useCeil: typeof import('../../node_modules/@vueuse/math')['useCeil']
  const useCheckout: typeof import('../../composables/useCheckout')['useCheckout']
  const useClamp: typeof import('../../node_modules/@vueuse/math')['useClamp']
  const useClipboard: typeof import('@vueuse/core')['useClipboard']
  const useClipboardItems: typeof import('@vueuse/core')['useClipboardItems']
  const useCloned: typeof import('@vueuse/core')['useCloned']
  const useCloudflareService: typeof import('../../composables/useCloudflareService')['useCloudflareService']
  const useCollections: typeof import('../../composables/useCollections')['useCollections']
  const useColorMode: typeof import('../../node_modules/@nuxtjs/color-mode/dist/runtime/composables')['useColorMode']
  const useConfig: typeof import('../../composables/useConfig')['useConfig']
  const useConfirmDialog: typeof import('@vueuse/core')['useConfirmDialog']
  const useCookie: typeof import('../../node_modules/nuxt/dist/app/composables/cookie')['useCookie']
  const useCookieLocale: typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useCookieLocale']
  const useCopyToClipboard: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useCopyToClipboard')['useCopyToClipboard']
  const useCountdown: typeof import('@vueuse/core')['useCountdown']
  const useCounter: typeof import('@vueuse/core')['useCounter']
  const useCssModule: typeof import('../../node_modules/vue')['useCssModule']
  const useCssVar: typeof import('@vueuse/core')['useCssVar']
  const useCssVars: typeof import('../../node_modules/vue')['useCssVars']
  const useCurrentElement: typeof import('@vueuse/core')['useCurrentElement']
  const useCustomer: typeof import('../../composables/useCustomer')['useCustomer']
  const useCycleList: typeof import('@vueuse/core')['useCycleList']
  const useDark: typeof import('@vueuse/core')['useDark']
  const useDateFormat: typeof import('@vueuse/core')['useDateFormat']
  const useDebounce: typeof import('@vueuse/core')['useDebounce']
  const useDebounceFn: typeof import('@vueuse/core')['useDebounceFn']
  const useDebouncedRefHistory: typeof import('@vueuse/core')['useDebouncedRefHistory']
  const useDeviceMotion: typeof import('@vueuse/core')['useDeviceMotion']
  const useDeviceOrientation: typeof import('@vueuse/core')['useDeviceOrientation']
  const useDevicePixelRatio: typeof import('@vueuse/core')['useDevicePixelRatio']
  const useDevicesList: typeof import('@vueuse/core')['useDevicesList']
  const useDisplayMedia: typeof import('@vueuse/core')['useDisplayMedia']
  const useDocumentVisibility: typeof import('@vueuse/core')['useDocumentVisibility']
  const useDraggable: typeof import('@vueuse/core')['useDraggable']
  const useDropZone: typeof import('@vueuse/core')['useDropZone']
  const useElementBounding: typeof import('@vueuse/core')['useElementBounding']
  const useElementByPoint: typeof import('@vueuse/core')['useElementByPoint']
  const useElementHover: typeof import('@vueuse/core')['useElementHover']
  const useElementSize: typeof import('@vueuse/core')['useElementSize']
  const useElementVisibility: typeof import('@vueuse/core')['useElementVisibility']
  const useError: typeof import('../../node_modules/nuxt/dist/app/composables/error')['useError']
  const useErrorHandler: typeof import('../../composables/useErrorHandler')['useErrorHandler']
  const useEventBus: typeof import('@vueuse/core')['useEventBus']
  const useEventListener: typeof import('@vueuse/core')['useEventListener']
  const useEventSource: typeof import('@vueuse/core')['useEventSource']
  const useEyeDropper: typeof import('@vueuse/core')['useEyeDropper']
  const useFavicon: typeof import('@vueuse/core')['useFavicon']
  const useFetch: typeof import('../../node_modules/nuxt/dist/app/composables/fetch')['useFetch']
  const useFileDialog: typeof import('@vueuse/core')['useFileDialog']
  const useFileSystemAccess: typeof import('@vueuse/core')['useFileSystemAccess']
  const useFloor: typeof import('../../node_modules/@vueuse/math')['useFloor']
  const useFocus: typeof import('@vueuse/core')['useFocus']
  const useFocusWithin: typeof import('@vueuse/core')['useFocusWithin']
  const useFormGroup: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useFormGroup')['useFormGroup']
  const useFps: typeof import('@vueuse/core')['useFps']
  const useFullscreen: typeof import('@vueuse/core')['useFullscreen']
  const useGamepad: typeof import('@vueuse/core')['useGamepad']
  const useGeolocation: typeof import('@vueuse/core')['useGeolocation']
  const useGoogleAds: typeof import('../../composables/useGoogleAds')['useGoogleAds']
  const useGtag: typeof import('../../../node_modules/nuxt-gtag/dist/runtime/composables/useGtag')['useGtag']
  const useHead: typeof import('../../node_modules/@unhead/vue')['useHead']
  const useHeadSafe: typeof import('../../node_modules/@unhead/vue')['useHeadSafe']
  const useHydration: typeof import('../../node_modules/nuxt/dist/app/composables/hydrate')['useHydration']
  const useI18n: typeof import('../../node_modules/vue-i18n/dist/vue-i18n')['useI18n']
  const useId: typeof import('../../node_modules/vue')['useId']
  const useIdle: typeof import('@vueuse/core')['useIdle']
  const useImage: typeof import('../../node_modules/@nuxt/image/dist/runtime/composables')['useImage']
  const useInfiniteScroll: typeof import('@vueuse/core')['useInfiniteScroll']
  const useInjectButtonGroup: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useButtonGroup')['useInjectButtonGroup']
  const useIntegratedCheckout: typeof import('../../composables/useIntegratedCheckout')['useIntegratedCheckout']
  const useIntersectionObserver: typeof import('@vueuse/core')['useIntersectionObserver']
  const useInterval: typeof import('@vueuse/core')['useInterval']
  const useIntervalFn: typeof import('@vueuse/core')['useIntervalFn']
  const useKeyModifier: typeof import('@vueuse/core')['useKeyModifier']
  const useLastChanged: typeof import('@vueuse/core')['useLastChanged']
  const useLazyAsyncData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useLazyAsyncData']
  const useLazyFetch: typeof import('../../node_modules/nuxt/dist/app/composables/fetch')['useLazyFetch']
  const useLink: typeof import('../../node_modules/vue-router')['useLink']
  const useLoadingIndicator: typeof import('../../node_modules/nuxt/dist/app/composables/loading-indicator')['useLoadingIndicator']
  const useLocalStorage: typeof import('@vueuse/core')['useLocalStorage']
  const useLocaleHead: typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useLocaleHead']
  const useLocalePath: typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useLocalePath']
  const useLocaleRoute: typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useLocaleRoute']
  const useMagicKeys: typeof import('@vueuse/core')['useMagicKeys']
  const useManualRefHistory: typeof import('@vueuse/core')['useManualRefHistory']
  const useMath: typeof import('../../node_modules/@vueuse/math')['useMath']
  const useMax: typeof import('../../node_modules/@vueuse/math')['useMax']
  const useMediaControls: typeof import('@vueuse/core')['useMediaControls']
  const useMediaQuery: typeof import('@vueuse/core')['useMediaQuery']
  const useMedusaCart: typeof import('../../composables/useMedusaCart')['useMedusaCart']
  const useMedusaPayment: typeof import('../../composables/useMedusaPayment')['useMedusaPayment']
  const useMedusaService: typeof import('../../composables/useMedusaService')['useMedusaService']
  const useMemoize: typeof import('@vueuse/core')['useMemoize']
  const useMemory: typeof import('@vueuse/core')['useMemory']
  const useMin: typeof import('../../node_modules/@vueuse/math')['useMin']
  const useModal: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useModal')['useModal']
  const useModel: typeof import('../../node_modules/vue')['useModel']
  const useMounted: typeof import('@vueuse/core')['useMounted']
  const useMouse: typeof import('@vueuse/core')['useMouse']
  const useMouseInElement: typeof import('@vueuse/core')['useMouseInElement']
  const useMousePressed: typeof import('@vueuse/core')['useMousePressed']
  const useMutationObserver: typeof import('@vueuse/core')['useMutationObserver']
  const useNavigatorLanguage: typeof import('@vueuse/core')['useNavigatorLanguage']
  const useNetwork: typeof import('@vueuse/core')['useNetwork']
  const useNow: typeof import('@vueuse/core')['useNow']
  const useNuxtApp: typeof import('../../node_modules/nuxt/dist/app/nuxt')['useNuxtApp']
  const useNuxtData: typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useNuxtData']
  const useNuxtDevTools: typeof import('../../node_modules/@nuxt/devtools/dist/runtime/use-nuxt-devtools')['useNuxtDevTools']
  const useObjectUrl: typeof import('@vueuse/core')['useObjectUrl']
  const useOffsetPagination: typeof import('@vueuse/core')['useOffsetPagination']
  const useOnline: typeof import('@vueuse/core')['useOnline']
  const useOrder: typeof import('../../composables/useOrder')['useOrder']
  const usePageLeave: typeof import('@vueuse/core')['usePageLeave']
  const useParallax: typeof import('@vueuse/core')['useParallax']
  const useParentElement: typeof import('@vueuse/core')['useParentElement']
  const usePaymentProviders: typeof import('../../composables/usePaymentProviders')['usePaymentProviders']
  const usePerformanceObserver: typeof import('@vueuse/core')['usePerformanceObserver']
  const usePermission: typeof import('@vueuse/core')['usePermission']
  const usePinia: typeof import('../../node_modules/@pinia/nuxt/dist/runtime/composables')['usePinia']
  const usePointer: typeof import('@vueuse/core')['usePointer']
  const usePointerLock: typeof import('@vueuse/core')['usePointerLock']
  const usePointerSwipe: typeof import('@vueuse/core')['usePointerSwipe']
  const usePopper: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/usePopper')['usePopper']
  const usePrecision: typeof import('../../node_modules/@vueuse/math')['usePrecision']
  const usePreferredColorScheme: typeof import('@vueuse/core')['usePreferredColorScheme']
  const usePreferredContrast: typeof import('@vueuse/core')['usePreferredContrast']
  const usePreferredDark: typeof import('@vueuse/core')['usePreferredDark']
  const usePreferredLanguages: typeof import('@vueuse/core')['usePreferredLanguages']
  const usePreferredReducedMotion: typeof import('@vueuse/core')['usePreferredReducedMotion']
  const usePreferredReducedTransparency: typeof import('@vueuse/core')['usePreferredReducedTransparency']
  const usePreviewMode: typeof import('../../node_modules/nuxt/dist/app/composables/preview')['usePreviewMode']
  const usePrevious: typeof import('@vueuse/core')['usePrevious']
  const useProduct: typeof import('../../composables/useProduct')['useProduct']
  const useProductDescription: typeof import('../../composables/useProductDescription')['useProductDescription']
  const useProductReviews: typeof import('../../composables/useProductReviews')['useProductReviews']
  const useProjection: typeof import('../../node_modules/@vueuse/math')['useProjection']
  const useProvideButtonGroup: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useButtonGroup')['useProvideButtonGroup']
  const useRafFn: typeof import('@vueuse/core')['useRafFn']
  const useRecommendations: typeof import('../../composables/useRecommendations')['useRecommendations']
  const useRefHistory: typeof import('@vueuse/core')['useRefHistory']
  const useRequestEvent: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestEvent']
  const useRequestFetch: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestFetch']
  const useRequestHeader: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestHeader']
  const useRequestHeaders: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestHeaders']
  const useRequestURL: typeof import('../../node_modules/nuxt/dist/app/composables/url')['useRequestURL']
  const useResizeObserver: typeof import('@vueuse/core')['useResizeObserver']
  const useResponseHeader: typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useResponseHeader']
  const useRound: typeof import('../../node_modules/@vueuse/math')['useRound']
  const useRoute: typeof import('../../node_modules/nuxt/dist/app/composables/router')['useRoute']
  const useRouteAnnouncer: typeof import('../../node_modules/nuxt/dist/app/composables/route-announcer')['useRouteAnnouncer']
  const useRouteBaseName: typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useRouteBaseName']
  const useRouter: typeof import('../../node_modules/nuxt/dist/app/composables/router')['useRouter']
  const useRuntimeConfig: typeof import('../../node_modules/nuxt/dist/app/nuxt')['useRuntimeConfig']
  const useRuntimeHook: typeof import('../../node_modules/nuxt/dist/app/composables/runtime-hook')['useRuntimeHook']
  const useSSRWidth: typeof import('@vueuse/core')['useSSRWidth']
  const useScreenOrientation: typeof import('@vueuse/core')['useScreenOrientation']
  const useScreenSafeArea: typeof import('@vueuse/core')['useScreenSafeArea']
  const useScript: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScript']
  const useScriptClarity: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptClarity']
  const useScriptCloudflareWebAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCloudflareWebAnalytics']
  const useScriptCrisp: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCrisp']
  const useScriptEventPage: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptEventPage']
  const useScriptFathomAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptFathomAnalytics']
  const useScriptGoogleAdsense: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAdsense']
  const useScriptGoogleAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAnalytics']
  const useScriptGoogleMaps: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleMaps']
  const useScriptGoogleTagManager: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleTagManager']
  const useScriptHotjar: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptHotjar']
  const useScriptIntercom: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptIntercom']
  const useScriptLemonSqueezy: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptLemonSqueezy']
  const useScriptMatomoAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMatomoAnalytics']
  const useScriptMetaPixel: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMetaPixel']
  const useScriptNpm: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptNpm']
  const useScriptPlausibleAnalytics: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptPlausibleAnalytics']
  const useScriptSegment: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptSegment']
  const useScriptStripe: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptStripe']
  const useScriptTag: typeof import('@vueuse/core')['useScriptTag']
  const useScriptTriggerConsent: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerConsent']
  const useScriptTriggerElement: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerElement']
  const useScriptVimeoPlayer: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptVimeoPlayer']
  const useScriptXPixel: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptXPixel']
  const useScriptYouTubePlayer: typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptYouTubePlayer']
  const useScroll: typeof import('@vueuse/core')['useScroll']
  const useScrollLock: typeof import('@vueuse/core')['useScrollLock']
  const useSeoMeta: typeof import('../../node_modules/@unhead/vue')['useSeoMeta']
  const useServerHead: typeof import('../../node_modules/@unhead/vue')['useServerHead']
  const useServerHeadSafe: typeof import('../../node_modules/@unhead/vue')['useServerHeadSafe']
  const useServerSeoMeta: typeof import('../../node_modules/@unhead/vue')['useServerSeoMeta']
  const useSessionStorage: typeof import('@vueuse/core')['useSessionStorage']
  const useSetI18nParams: typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useSetI18nParams']
  const useShadowRoot: typeof import('../../node_modules/vue')['useShadowRoot']
  const useShare: typeof import('@vueuse/core')['useShare']
  const useShortcuts: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useShortcuts')['useShortcuts']
  const useSlideover: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useSlideover')['useSlideover']
  const useSlots: typeof import('../../node_modules/vue')['useSlots']
  const useSorted: typeof import('@vueuse/core')['useSorted']
  const useSpeechRecognition: typeof import('@vueuse/core')['useSpeechRecognition']
  const useSpeechSynthesis: typeof import('@vueuse/core')['useSpeechSynthesis']
  const useState: typeof import('../../node_modules/nuxt/dist/app/composables/state')['useState']
  const useStepper: typeof import('@vueuse/core')['useStepper']
  const useStorageAsync: typeof import('@vueuse/core')['useStorageAsync']
  const useStripe: typeof import('../../composables/useStripe')['useStripe']
  const useStyleTag: typeof import('@vueuse/core')['useStyleTag']
  const useSum: typeof import('../../node_modules/@vueuse/math')['useSum']
  const useSupported: typeof import('@vueuse/core')['useSupported']
  const useSwipe: typeof import('@vueuse/core')['useSwipe']
  const useSwitchLocalePath: typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useSwitchLocalePath']
  const useTemplateRef: typeof import('../../node_modules/vue')['useTemplateRef']
  const useTemplateRefsList: typeof import('@vueuse/core')['useTemplateRefsList']
  const useTextDirection: typeof import('@vueuse/core')['useTextDirection']
  const useTextSelection: typeof import('@vueuse/core')['useTextSelection']
  const useTextareaAutosize: typeof import('@vueuse/core')['useTextareaAutosize']
  const useThrottle: typeof import('@vueuse/core')['useThrottle']
  const useThrottleFn: typeof import('@vueuse/core')['useThrottleFn']
  const useThrottledRefHistory: typeof import('@vueuse/core')['useThrottledRefHistory']
  const useTimeAgo: typeof import('@vueuse/core')['useTimeAgo']
  const useTimeout: typeof import('@vueuse/core')['useTimeout']
  const useTimeoutFn: typeof import('@vueuse/core')['useTimeoutFn']
  const useTimeoutPoll: typeof import('@vueuse/core')['useTimeoutPoll']
  const useTimer: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useTimer')['useTimer']
  const useTimestamp: typeof import('@vueuse/core')['useTimestamp']
  const useTitle: typeof import('@vueuse/core')['useTitle']
  const useToNumber: typeof import('@vueuse/core')['useToNumber']
  const useToString: typeof import('@vueuse/core')['useToString']
  const useToast: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useToast')['useToast']
  const useToggle: typeof import('@vueuse/core')['useToggle']
  const useTrackEvent: typeof import('../../../node_modules/nuxt-gtag/dist/runtime/composables/useTrackEvent')['useTrackEvent']
  const useTransition: typeof import('@vueuse/core')['useTransition']
  const useTransitionState: typeof import('../../node_modules/vue')['useTransitionState']
  const useTrunc: typeof import('../../node_modules/@vueuse/math')['useTrunc']
  const useTrustedShop: typeof import('../../composables/useTrustedShop')['useTrustedShop']
  const useUI: typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useUI')['useUI']
  const useUrlSearchParams: typeof import('@vueuse/core')['useUrlSearchParams']
  const useUserMedia: typeof import('@vueuse/core')['useUserMedia']
  const useUtilities: typeof import('../../composables/useUtilities')['useUtilities']
  const useVModel: typeof import('@vueuse/core')['useVModel']
  const useVModels: typeof import('@vueuse/core')['useVModels']
  const useVibrate: typeof import('@vueuse/core')['useVibrate']
  const useVirtualList: typeof import('@vueuse/core')['useVirtualList']
  const useWakeLock: typeof import('@vueuse/core')['useWakeLock']
  const useWebNotification: typeof import('@vueuse/core')['useWebNotification']
  const useWebSocket: typeof import('@vueuse/core')['useWebSocket']
  const useWebWorker: typeof import('@vueuse/core')['useWebWorker']
  const useWebWorkerFn: typeof import('@vueuse/core')['useWebWorkerFn']
  const useWindowFocus: typeof import('@vueuse/core')['useWindowFocus']
  const useWindowScroll: typeof import('@vueuse/core')['useWindowScroll']
  const useWindowSize: typeof import('@vueuse/core')['useWindowSize']
  const watch: typeof import('../../node_modules/vue')['watch']
  const watchArray: typeof import('@vueuse/core')['watchArray']
  const watchAtMost: typeof import('@vueuse/core')['watchAtMost']
  const watchDebounced: typeof import('@vueuse/core')['watchDebounced']
  const watchDeep: typeof import('@vueuse/core')['watchDeep']
  const watchEffect: typeof import('../../node_modules/vue')['watchEffect']
  const watchIgnorable: typeof import('@vueuse/core')['watchIgnorable']
  const watchImmediate: typeof import('@vueuse/core')['watchImmediate']
  const watchOnce: typeof import('@vueuse/core')['watchOnce']
  const watchPausable: typeof import('@vueuse/core')['watchPausable']
  const watchPostEffect: typeof import('../../node_modules/vue')['watchPostEffect']
  const watchSyncEffect: typeof import('../../node_modules/vue')['watchSyncEffect']
  const watchThrottled: typeof import('@vueuse/core')['watchThrottled']
  const watchTriggerable: typeof import('@vueuse/core')['watchTriggerable']
  const watchWithFilter: typeof import('@vueuse/core')['watchWithFilter']
  const whenever: typeof import('@vueuse/core')['whenever']
  const withCtx: typeof import('../../node_modules/vue')['withCtx']
  const withDirectives: typeof import('../../node_modules/vue')['withDirectives']
  const withKeys: typeof import('../../node_modules/vue')['withKeys']
  const withMemo: typeof import('../../node_modules/vue')['withMemo']
  const withModifiers: typeof import('../../node_modules/vue')['withModifiers']
  const withScopeId: typeof import('../../node_modules/vue')['withScopeId']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { Component, ComponentPublicInstance, ComputedRef, DirectiveBinding, ExtractDefaultPropTypes, ExtractPropTypes, ExtractPublicPropTypes, InjectionKey, PropType, Ref, MaybeRef, MaybeRefOrGetter, VNode, WritableComputedRef } from '../../node_modules/vue'
  import('../../node_modules/vue')
  // @ts-ignore
  export type { AuthUser } from '../../composables/useAuth'
  import('../../composables/useAuth')
  // @ts-ignore
  export type { CartLine } from '../../composables/useCart'
  import('../../composables/useCart')
  // @ts-ignore
  export type { Customer, Address, ShippingOption, PaymentOption } from '../../composables/useCheckout'
  import('../../composables/useCheckout')
  // @ts-ignore
  export type { CartItem } from '../../composables/useMedusaCart'
  import('../../composables/useMedusaCart')
  // @ts-ignore
  export type { OrderItem, Order } from '../../composables/useOrder'
  import('../../composables/useOrder')
  // @ts-ignore
  export type { ProductRating, ProductReview, ProductReviewData, ReviewError } from '../../composables/useProductReviews'
  import('../../composables/useProductReviews')
  // @ts-ignore
  export type { TrustedShopProduct, TrustedShopResponse, TrustedShopStatusResponse } from '../../composables/useTrustedShop'
  import('../../composables/useTrustedShop')
  // @ts-ignore
  export type { ProductPrice } from '../../utils/price'
  import('../../utils/price')
}
// for vue template auto import
import { UnwrapRef } from 'vue'
declare module 'vue' {
  interface ComponentCustomProperties {
    readonly _useShortcuts: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useShortcuts')['_useShortcuts']>
    readonly abortNavigation: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['abortNavigation']>
    readonly addRouteMiddleware: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['addRouteMiddleware']>
    readonly asyncComputed: UnwrapRef<typeof import('@vueuse/core')['asyncComputed']>
    readonly autoResetRef: UnwrapRef<typeof import('@vueuse/core')['autoResetRef']>
    readonly callOnce: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/once')['callOnce']>
    readonly cancelIdleCallback: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/idle-callback')['cancelIdleCallback']>
    readonly clearError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['clearError']>
    readonly clearNuxtData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['clearNuxtData']>
    readonly clearNuxtState: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/state')['clearNuxtState']>
    readonly cloudflareApiFetch: UnwrapRef<typeof import('../../utils/api')['cloudflareApiFetch']>
    readonly computed: UnwrapRef<typeof import('../../node_modules/vue')['computed']>
    readonly computedAsync: UnwrapRef<typeof import('@vueuse/core')['computedAsync']>
    readonly computedEager: UnwrapRef<typeof import('@vueuse/core')['computedEager']>
    readonly computedInject: UnwrapRef<typeof import('@vueuse/core')['computedInject']>
    readonly computedWithControl: UnwrapRef<typeof import('@vueuse/core')['computedWithControl']>
    readonly controlledComputed: UnwrapRef<typeof import('@vueuse/core')['controlledComputed']>
    readonly controlledRef: UnwrapRef<typeof import('@vueuse/core')['controlledRef']>
    readonly createError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['createError']>
    readonly createEventHook: UnwrapRef<typeof import('@vueuse/core')['createEventHook']>
    readonly createGenericProjection: UnwrapRef<typeof import('../../node_modules/@vueuse/math')['createGenericProjection']>
    readonly createGlobalState: UnwrapRef<typeof import('@vueuse/core')['createGlobalState']>
    readonly createInjectionState: UnwrapRef<typeof import('@vueuse/core')['createInjectionState']>
    readonly createPopper: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/usePopper')['createPopper']>
    readonly createProjection: UnwrapRef<typeof import('../../node_modules/@vueuse/math')['createProjection']>
    readonly createReactiveFn: UnwrapRef<typeof import('@vueuse/core')['createReactiveFn']>
    readonly createReusableTemplate: UnwrapRef<typeof import('@vueuse/core')['createReusableTemplate']>
    readonly createSharedComposable: UnwrapRef<typeof import('@vueuse/core')['createSharedComposable']>
    readonly createTemplatePromise: UnwrapRef<typeof import('@vueuse/core')['createTemplatePromise']>
    readonly createUnrefFn: UnwrapRef<typeof import('@vueuse/core')['createUnrefFn']>
    readonly customRef: UnwrapRef<typeof import('../../node_modules/vue')['customRef']>
    readonly debouncedRef: UnwrapRef<typeof import('@vueuse/core')['debouncedRef']>
    readonly debouncedWatch: UnwrapRef<typeof import('@vueuse/core')['debouncedWatch']>
    readonly defineAppConfig: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['defineAppConfig']>
    readonly defineAsyncComponent: UnwrapRef<typeof import('../../node_modules/vue')['defineAsyncComponent']>
    readonly defineComponent: UnwrapRef<typeof import('../../node_modules/vue')['defineComponent']>
    readonly defineI18nConfig: UnwrapRef<typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['defineI18nConfig']>
    readonly defineI18nLocale: UnwrapRef<typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['defineI18nLocale']>
    readonly defineI18nRoute: UnwrapRef<typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['defineI18nRoute']>
    readonly defineNuxtComponent: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/component')['defineNuxtComponent']>
    readonly defineNuxtLink: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/components/nuxt-link')['defineNuxtLink']>
    readonly defineNuxtPlugin: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['defineNuxtPlugin']>
    readonly defineNuxtRouteMiddleware: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['defineNuxtRouteMiddleware']>
    readonly definePageMeta: UnwrapRef<typeof import('../../node_modules/nuxt/dist/pages/runtime/composables')['definePageMeta']>
    readonly definePayloadPlugin: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['definePayloadPlugin']>
    readonly definePayloadReducer: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['definePayloadReducer']>
    readonly definePayloadReviver: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['definePayloadReviver']>
    readonly defineShortcuts: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/defineShortcuts')['defineShortcuts']>
    readonly eagerComputed: UnwrapRef<typeof import('@vueuse/core')['eagerComputed']>
    readonly effect: UnwrapRef<typeof import('../../node_modules/vue')['effect']>
    readonly effectScope: UnwrapRef<typeof import('../../node_modules/vue')['effectScope']>
    readonly extendRef: UnwrapRef<typeof import('@vueuse/core')['extendRef']>
    readonly fetchPricesById: UnwrapRef<typeof import('../../utils/price')['fetchPricesById']>
    readonly fetchSinglePrice: UnwrapRef<typeof import('../../utils/price')['fetchSinglePrice']>
    readonly getAppManifest: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/manifest')['getAppManifest']>
    readonly getCurrentInstance: UnwrapRef<typeof import('../../node_modules/vue')['getCurrentInstance']>
    readonly getCurrentScope: UnwrapRef<typeof import('../../node_modules/vue')['getCurrentScope']>
    readonly getRouteRules: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/manifest')['getRouteRules']>
    readonly getServerSession: UnwrapRef<typeof import('../../composables/useAuth')['getServerSession']>
    readonly h: UnwrapRef<typeof import('../../node_modules/vue')['h']>
    readonly hasInjectionContext: UnwrapRef<typeof import('../../node_modules/vue')['hasInjectionContext']>
    readonly ignorableWatch: UnwrapRef<typeof import('@vueuse/core')['ignorableWatch']>
    readonly inject: UnwrapRef<typeof import('../../node_modules/vue')['inject']>
    readonly injectHead: UnwrapRef<typeof import('../../node_modules/@unhead/vue')['injectHead']>
    readonly injectLocal: UnwrapRef<typeof import('@vueuse/core')['injectLocal']>
    readonly isDefined: UnwrapRef<typeof import('@vueuse/core')['isDefined']>
    readonly isNuxtError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['isNuxtError']>
    readonly isPrerendered: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['isPrerendered']>
    readonly isProxy: UnwrapRef<typeof import('../../node_modules/vue')['isProxy']>
    readonly isReactive: UnwrapRef<typeof import('../../node_modules/vue')['isReactive']>
    readonly isReadonly: UnwrapRef<typeof import('../../node_modules/vue')['isReadonly']>
    readonly isRef: UnwrapRef<typeof import('../../node_modules/vue')['isRef']>
    readonly isShallow: UnwrapRef<typeof import('../../node_modules/vue')['isShallow']>
    readonly isVue2: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue2']>
    readonly isVue3: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/vue-demi')['isVue3']>
    readonly loadPayload: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['loadPayload']>
    readonly logicAnd: UnwrapRef<typeof import('../../node_modules/@vueuse/math')['logicAnd']>
    readonly logicNot: UnwrapRef<typeof import('../../node_modules/@vueuse/math')['logicNot']>
    readonly logicOr: UnwrapRef<typeof import('../../node_modules/@vueuse/math')['logicOr']>
    readonly makeDestructurable: UnwrapRef<typeof import('@vueuse/core')['makeDestructurable']>
    readonly markRaw: UnwrapRef<typeof import('../../node_modules/vue')['markRaw']>
    readonly mergeModels: UnwrapRef<typeof import('../../node_modules/vue')['mergeModels']>
    readonly modalInjectionKey: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useModal')['modalInjectionKey']>
    readonly navigateTo: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['navigateTo']>
    readonly nextTick: UnwrapRef<typeof import('../../node_modules/vue')['nextTick']>
    readonly onActivated: UnwrapRef<typeof import('../../node_modules/vue')['onActivated']>
    readonly onBeforeMount: UnwrapRef<typeof import('../../node_modules/vue')['onBeforeMount']>
    readonly onBeforeRouteLeave: UnwrapRef<typeof import('../../node_modules/vue-router')['onBeforeRouteLeave']>
    readonly onBeforeRouteUpdate: UnwrapRef<typeof import('../../node_modules/vue-router')['onBeforeRouteUpdate']>
    readonly onBeforeUnmount: UnwrapRef<typeof import('../../node_modules/vue')['onBeforeUnmount']>
    readonly onBeforeUpdate: UnwrapRef<typeof import('../../node_modules/vue')['onBeforeUpdate']>
    readonly onClickOutside: UnwrapRef<typeof import('@vueuse/core')['onClickOutside']>
    readonly onDeactivated: UnwrapRef<typeof import('../../node_modules/vue')['onDeactivated']>
    readonly onElementRemoval: UnwrapRef<typeof import('@vueuse/core')['onElementRemoval']>
    readonly onErrorCaptured: UnwrapRef<typeof import('../../node_modules/vue')['onErrorCaptured']>
    readonly onKeyStroke: UnwrapRef<typeof import('@vueuse/core')['onKeyStroke']>
    readonly onLongPress: UnwrapRef<typeof import('@vueuse/core')['onLongPress']>
    readonly onMounted: UnwrapRef<typeof import('../../node_modules/vue')['onMounted']>
    readonly onNuxtReady: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ready')['onNuxtReady']>
    readonly onPrehydrate: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['onPrehydrate']>
    readonly onRenderTracked: UnwrapRef<typeof import('../../node_modules/vue')['onRenderTracked']>
    readonly onRenderTriggered: UnwrapRef<typeof import('../../node_modules/vue')['onRenderTriggered']>
    readonly onScopeDispose: UnwrapRef<typeof import('../../node_modules/vue')['onScopeDispose']>
    readonly onServerPrefetch: UnwrapRef<typeof import('../../node_modules/vue')['onServerPrefetch']>
    readonly onStartTyping: UnwrapRef<typeof import('@vueuse/core')['onStartTyping']>
    readonly onUnmounted: UnwrapRef<typeof import('../../node_modules/vue')['onUnmounted']>
    readonly onUpdated: UnwrapRef<typeof import('../../node_modules/vue')['onUpdated']>
    readonly pausableWatch: UnwrapRef<typeof import('@vueuse/core')['pausableWatch']>
    readonly prefetchComponents: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/preload')['prefetchComponents']>
    readonly preloadComponents: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/preload')['preloadComponents']>
    readonly preloadPayload: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/payload')['preloadPayload']>
    readonly preloadRouteComponents: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/preload')['preloadRouteComponents']>
    readonly prerenderRoutes: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['prerenderRoutes']>
    readonly provide: UnwrapRef<typeof import('../../node_modules/vue')['provide']>
    readonly provideLocal: UnwrapRef<typeof import('@vueuse/core')['provideLocal']>
    readonly proxyRefs: UnwrapRef<typeof import('../../node_modules/vue')['proxyRefs']>
    readonly reactify: UnwrapRef<typeof import('@vueuse/core')['reactify']>
    readonly reactifyObject: UnwrapRef<typeof import('@vueuse/core')['reactifyObject']>
    readonly reactive: UnwrapRef<typeof import('../../node_modules/vue')['reactive']>
    readonly reactiveComputed: UnwrapRef<typeof import('@vueuse/core')['reactiveComputed']>
    readonly reactiveOmit: UnwrapRef<typeof import('@vueuse/core')['reactiveOmit']>
    readonly reactivePick: UnwrapRef<typeof import('@vueuse/core')['reactivePick']>
    readonly readonly: UnwrapRef<typeof import('../../node_modules/vue')['readonly']>
    readonly ref: UnwrapRef<typeof import('../../node_modules/vue')['ref']>
    readonly refAutoReset: UnwrapRef<typeof import('@vueuse/core')['refAutoReset']>
    readonly refDebounced: UnwrapRef<typeof import('@vueuse/core')['refDebounced']>
    readonly refDefault: UnwrapRef<typeof import('@vueuse/core')['refDefault']>
    readonly refThrottled: UnwrapRef<typeof import('@vueuse/core')['refThrottled']>
    readonly refWithControl: UnwrapRef<typeof import('@vueuse/core')['refWithControl']>
    readonly refreshCookie: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/cookie')['refreshCookie']>
    readonly refreshNuxtData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['refreshNuxtData']>
    readonly reloadNuxtApp: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/chunk')['reloadNuxtApp']>
    readonly requestIdleCallback: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/idle-callback')['requestIdleCallback']>
    readonly resolveComponent: UnwrapRef<typeof import('../../node_modules/vue')['resolveComponent']>
    readonly resolveRef: UnwrapRef<typeof import('@vueuse/core')['resolveRef']>
    readonly resolveUnref: UnwrapRef<typeof import('@vueuse/core')['resolveUnref']>
    readonly setInterval: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/compat/interval')['setInterval']>
    readonly setPageLayout: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['setPageLayout']>
    readonly setResponseStatus: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['setResponseStatus']>
    readonly shallowReactive: UnwrapRef<typeof import('../../node_modules/vue')['shallowReactive']>
    readonly shallowReadonly: UnwrapRef<typeof import('../../node_modules/vue')['shallowReadonly']>
    readonly shallowRef: UnwrapRef<typeof import('../../node_modules/vue')['shallowRef']>
    readonly showError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['showError']>
    readonly slidOverInjectionKey: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useSlideover')['slidOverInjectionKey']>
    readonly syncRef: UnwrapRef<typeof import('@vueuse/core')['syncRef']>
    readonly syncRefs: UnwrapRef<typeof import('@vueuse/core')['syncRefs']>
    readonly templateRef: UnwrapRef<typeof import('@vueuse/core')['templateRef']>
    readonly throttledRef: UnwrapRef<typeof import('@vueuse/core')['throttledRef']>
    readonly throttledWatch: UnwrapRef<typeof import('@vueuse/core')['throttledWatch']>
    readonly toRaw: UnwrapRef<typeof import('../../node_modules/vue')['toRaw']>
    readonly toReactive: UnwrapRef<typeof import('@vueuse/core')['toReactive']>
    readonly toRef: UnwrapRef<typeof import('../../node_modules/vue')['toRef']>
    readonly toRefs: UnwrapRef<typeof import('../../node_modules/vue')['toRefs']>
    readonly toValue: UnwrapRef<typeof import('../../node_modules/vue')['toValue']>
    readonly triggerRef: UnwrapRef<typeof import('../../node_modules/vue')['triggerRef']>
    readonly tryOnBeforeMount: UnwrapRef<typeof import('@vueuse/core')['tryOnBeforeMount']>
    readonly tryOnBeforeUnmount: UnwrapRef<typeof import('@vueuse/core')['tryOnBeforeUnmount']>
    readonly tryOnMounted: UnwrapRef<typeof import('@vueuse/core')['tryOnMounted']>
    readonly tryOnScopeDispose: UnwrapRef<typeof import('@vueuse/core')['tryOnScopeDispose']>
    readonly tryOnUnmounted: UnwrapRef<typeof import('@vueuse/core')['tryOnUnmounted']>
    readonly tryUseNuxtApp: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['tryUseNuxtApp']>
    readonly unref: UnwrapRef<typeof import('../../node_modules/vue')['unref']>
    readonly unrefElement: UnwrapRef<typeof import('@vueuse/core')['unrefElement']>
    readonly until: UnwrapRef<typeof import('@vueuse/core')['until']>
    readonly updateAppConfig: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/config')['updateAppConfig']>
    readonly useAbs: UnwrapRef<typeof import('../../node_modules/@vueuse/math')['useAbs']>
    readonly useActiveElement: UnwrapRef<typeof import('@vueuse/core')['useActiveElement']>
    readonly useAnimate: UnwrapRef<typeof import('@vueuse/core')['useAnimate']>
    readonly useAppConfig: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/config')['useAppConfig']>
    readonly useArrayDifference: UnwrapRef<typeof import('@vueuse/core')['useArrayDifference']>
    readonly useArrayEvery: UnwrapRef<typeof import('@vueuse/core')['useArrayEvery']>
    readonly useArrayFilter: UnwrapRef<typeof import('@vueuse/core')['useArrayFilter']>
    readonly useArrayFind: UnwrapRef<typeof import('@vueuse/core')['useArrayFind']>
    readonly useArrayFindIndex: UnwrapRef<typeof import('@vueuse/core')['useArrayFindIndex']>
    readonly useArrayFindLast: UnwrapRef<typeof import('@vueuse/core')['useArrayFindLast']>
    readonly useArrayIncludes: UnwrapRef<typeof import('@vueuse/core')['useArrayIncludes']>
    readonly useArrayJoin: UnwrapRef<typeof import('@vueuse/core')['useArrayJoin']>
    readonly useArrayMap: UnwrapRef<typeof import('@vueuse/core')['useArrayMap']>
    readonly useArrayReduce: UnwrapRef<typeof import('@vueuse/core')['useArrayReduce']>
    readonly useArraySome: UnwrapRef<typeof import('@vueuse/core')['useArraySome']>
    readonly useArrayUnique: UnwrapRef<typeof import('@vueuse/core')['useArrayUnique']>
    readonly useAsyncData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useAsyncData']>
    readonly useAsyncQueue: UnwrapRef<typeof import('@vueuse/core')['useAsyncQueue']>
    readonly useAsyncState: UnwrapRef<typeof import('@vueuse/core')['useAsyncState']>
    readonly useAttrs: UnwrapRef<typeof import('../../node_modules/vue')['useAttrs']>
    readonly useAuth: UnwrapRef<typeof import('../../composables/useAuth')['useAuth']>
    readonly useAverage: UnwrapRef<typeof import('../../node_modules/@vueuse/math')['useAverage']>
    readonly useBase64: UnwrapRef<typeof import('@vueuse/core')['useBase64']>
    readonly useBattery: UnwrapRef<typeof import('@vueuse/core')['useBattery']>
    readonly useBluetooth: UnwrapRef<typeof import('@vueuse/core')['useBluetooth']>
    readonly useBreakpoints: UnwrapRef<typeof import('@vueuse/core')['useBreakpoints']>
    readonly useBroadcastChannel: UnwrapRef<typeof import('@vueuse/core')['useBroadcastChannel']>
    readonly useBrowserLocale: UnwrapRef<typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useBrowserLocale']>
    readonly useBrowserLocation: UnwrapRef<typeof import('@vueuse/core')['useBrowserLocation']>
    readonly useCached: UnwrapRef<typeof import('@vueuse/core')['useCached']>
    readonly useCarouselScroll: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useCarouselScroll')['useCarouselScroll']>
    readonly useCart: UnwrapRef<typeof import('../../composables/useCart')['useCart']>
    readonly useCartContext: UnwrapRef<typeof import('../../composables/useCartContext')['useCartContext']>
    readonly useCartContextInject: UnwrapRef<typeof import('../../composables/useCartContext')['useCartContextInject']>
    readonly useCartContextProvider: UnwrapRef<typeof import('../../composables/useCartContext')['useCartContextProvider']>
    readonly useCeil: UnwrapRef<typeof import('../../node_modules/@vueuse/math')['useCeil']>
    readonly useCheckout: UnwrapRef<typeof import('../../composables/useCheckout')['useCheckout']>
    readonly useClamp: UnwrapRef<typeof import('../../node_modules/@vueuse/math')['useClamp']>
    readonly useClipboard: UnwrapRef<typeof import('@vueuse/core')['useClipboard']>
    readonly useClipboardItems: UnwrapRef<typeof import('@vueuse/core')['useClipboardItems']>
    readonly useCloned: UnwrapRef<typeof import('@vueuse/core')['useCloned']>
    readonly useCloudflareService: UnwrapRef<typeof import('../../composables/useCloudflareService')['useCloudflareService']>
    readonly useCollections: UnwrapRef<typeof import('../../composables/useCollections')['useCollections']>
    readonly useColorMode: UnwrapRef<typeof import('../../node_modules/@nuxtjs/color-mode/dist/runtime/composables')['useColorMode']>
    readonly useConfig: UnwrapRef<typeof import('../../composables/useConfig')['useConfig']>
    readonly useConfirmDialog: UnwrapRef<typeof import('@vueuse/core')['useConfirmDialog']>
    readonly useCookie: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/cookie')['useCookie']>
    readonly useCookieLocale: UnwrapRef<typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useCookieLocale']>
    readonly useCopyToClipboard: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useCopyToClipboard')['useCopyToClipboard']>
    readonly useCountdown: UnwrapRef<typeof import('@vueuse/core')['useCountdown']>
    readonly useCounter: UnwrapRef<typeof import('@vueuse/core')['useCounter']>
    readonly useCssModule: UnwrapRef<typeof import('../../node_modules/vue')['useCssModule']>
    readonly useCssVar: UnwrapRef<typeof import('@vueuse/core')['useCssVar']>
    readonly useCssVars: UnwrapRef<typeof import('../../node_modules/vue')['useCssVars']>
    readonly useCurrentElement: UnwrapRef<typeof import('@vueuse/core')['useCurrentElement']>
    readonly useCustomer: UnwrapRef<typeof import('../../composables/useCustomer')['useCustomer']>
    readonly useCycleList: UnwrapRef<typeof import('@vueuse/core')['useCycleList']>
    readonly useDark: UnwrapRef<typeof import('@vueuse/core')['useDark']>
    readonly useDateFormat: UnwrapRef<typeof import('@vueuse/core')['useDateFormat']>
    readonly useDebounce: UnwrapRef<typeof import('@vueuse/core')['useDebounce']>
    readonly useDebounceFn: UnwrapRef<typeof import('@vueuse/core')['useDebounceFn']>
    readonly useDebouncedRefHistory: UnwrapRef<typeof import('@vueuse/core')['useDebouncedRefHistory']>
    readonly useDeviceMotion: UnwrapRef<typeof import('@vueuse/core')['useDeviceMotion']>
    readonly useDeviceOrientation: UnwrapRef<typeof import('@vueuse/core')['useDeviceOrientation']>
    readonly useDevicePixelRatio: UnwrapRef<typeof import('@vueuse/core')['useDevicePixelRatio']>
    readonly useDevicesList: UnwrapRef<typeof import('@vueuse/core')['useDevicesList']>
    readonly useDisplayMedia: UnwrapRef<typeof import('@vueuse/core')['useDisplayMedia']>
    readonly useDocumentVisibility: UnwrapRef<typeof import('@vueuse/core')['useDocumentVisibility']>
    readonly useDraggable: UnwrapRef<typeof import('@vueuse/core')['useDraggable']>
    readonly useDropZone: UnwrapRef<typeof import('@vueuse/core')['useDropZone']>
    readonly useElementBounding: UnwrapRef<typeof import('@vueuse/core')['useElementBounding']>
    readonly useElementByPoint: UnwrapRef<typeof import('@vueuse/core')['useElementByPoint']>
    readonly useElementHover: UnwrapRef<typeof import('@vueuse/core')['useElementHover']>
    readonly useElementSize: UnwrapRef<typeof import('@vueuse/core')['useElementSize']>
    readonly useElementVisibility: UnwrapRef<typeof import('@vueuse/core')['useElementVisibility']>
    readonly useError: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/error')['useError']>
    readonly useErrorHandler: UnwrapRef<typeof import('../../composables/useErrorHandler')['useErrorHandler']>
    readonly useEventBus: UnwrapRef<typeof import('@vueuse/core')['useEventBus']>
    readonly useEventListener: UnwrapRef<typeof import('@vueuse/core')['useEventListener']>
    readonly useEventSource: UnwrapRef<typeof import('@vueuse/core')['useEventSource']>
    readonly useEyeDropper: UnwrapRef<typeof import('@vueuse/core')['useEyeDropper']>
    readonly useFavicon: UnwrapRef<typeof import('@vueuse/core')['useFavicon']>
    readonly useFetch: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/fetch')['useFetch']>
    readonly useFileDialog: UnwrapRef<typeof import('@vueuse/core')['useFileDialog']>
    readonly useFileSystemAccess: UnwrapRef<typeof import('@vueuse/core')['useFileSystemAccess']>
    readonly useFloor: UnwrapRef<typeof import('../../node_modules/@vueuse/math')['useFloor']>
    readonly useFocus: UnwrapRef<typeof import('@vueuse/core')['useFocus']>
    readonly useFocusWithin: UnwrapRef<typeof import('@vueuse/core')['useFocusWithin']>
    readonly useFormGroup: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useFormGroup')['useFormGroup']>
    readonly useFps: UnwrapRef<typeof import('@vueuse/core')['useFps']>
    readonly useFullscreen: UnwrapRef<typeof import('@vueuse/core')['useFullscreen']>
    readonly useGamepad: UnwrapRef<typeof import('@vueuse/core')['useGamepad']>
    readonly useGeolocation: UnwrapRef<typeof import('@vueuse/core')['useGeolocation']>
    readonly useGoogleAds: UnwrapRef<typeof import('../../composables/useGoogleAds')['useGoogleAds']>
    readonly useGtag: UnwrapRef<typeof import('../../../node_modules/nuxt-gtag/dist/runtime/composables/useGtag')['useGtag']>
    readonly useHead: UnwrapRef<typeof import('../../node_modules/@unhead/vue')['useHead']>
    readonly useHeadSafe: UnwrapRef<typeof import('../../node_modules/@unhead/vue')['useHeadSafe']>
    readonly useHydration: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/hydrate')['useHydration']>
    readonly useI18n: UnwrapRef<typeof import('../../node_modules/vue-i18n/dist/vue-i18n')['useI18n']>
    readonly useId: UnwrapRef<typeof import('../../node_modules/vue')['useId']>
    readonly useIdle: UnwrapRef<typeof import('@vueuse/core')['useIdle']>
    readonly useImage: UnwrapRef<typeof import('../../node_modules/@nuxt/image/dist/runtime/composables')['useImage']>
    readonly useInfiniteScroll: UnwrapRef<typeof import('@vueuse/core')['useInfiniteScroll']>
    readonly useInjectButtonGroup: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useButtonGroup')['useInjectButtonGroup']>
    readonly useIntegratedCheckout: UnwrapRef<typeof import('../../composables/useIntegratedCheckout')['useIntegratedCheckout']>
    readonly useIntersectionObserver: UnwrapRef<typeof import('@vueuse/core')['useIntersectionObserver']>
    readonly useInterval: UnwrapRef<typeof import('@vueuse/core')['useInterval']>
    readonly useIntervalFn: UnwrapRef<typeof import('@vueuse/core')['useIntervalFn']>
    readonly useKeyModifier: UnwrapRef<typeof import('@vueuse/core')['useKeyModifier']>
    readonly useLastChanged: UnwrapRef<typeof import('@vueuse/core')['useLastChanged']>
    readonly useLazyAsyncData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useLazyAsyncData']>
    readonly useLazyFetch: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/fetch')['useLazyFetch']>
    readonly useLink: UnwrapRef<typeof import('../../node_modules/vue-router')['useLink']>
    readonly useLoadingIndicator: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/loading-indicator')['useLoadingIndicator']>
    readonly useLocalStorage: UnwrapRef<typeof import('@vueuse/core')['useLocalStorage']>
    readonly useLocaleHead: UnwrapRef<typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useLocaleHead']>
    readonly useLocalePath: UnwrapRef<typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useLocalePath']>
    readonly useLocaleRoute: UnwrapRef<typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useLocaleRoute']>
    readonly useMagicKeys: UnwrapRef<typeof import('@vueuse/core')['useMagicKeys']>
    readonly useManualRefHistory: UnwrapRef<typeof import('@vueuse/core')['useManualRefHistory']>
    readonly useMath: UnwrapRef<typeof import('../../node_modules/@vueuse/math')['useMath']>
    readonly useMax: UnwrapRef<typeof import('../../node_modules/@vueuse/math')['useMax']>
    readonly useMediaControls: UnwrapRef<typeof import('@vueuse/core')['useMediaControls']>
    readonly useMediaQuery: UnwrapRef<typeof import('@vueuse/core')['useMediaQuery']>
    readonly useMedusaCart: UnwrapRef<typeof import('../../composables/useMedusaCart')['useMedusaCart']>
    readonly useMedusaPayment: UnwrapRef<typeof import('../../composables/useMedusaPayment')['useMedusaPayment']>
    readonly useMedusaService: UnwrapRef<typeof import('../../composables/useMedusaService')['useMedusaService']>
    readonly useMemoize: UnwrapRef<typeof import('@vueuse/core')['useMemoize']>
    readonly useMemory: UnwrapRef<typeof import('@vueuse/core')['useMemory']>
    readonly useMin: UnwrapRef<typeof import('../../node_modules/@vueuse/math')['useMin']>
    readonly useModal: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useModal')['useModal']>
    readonly useModel: UnwrapRef<typeof import('../../node_modules/vue')['useModel']>
    readonly useMounted: UnwrapRef<typeof import('@vueuse/core')['useMounted']>
    readonly useMouse: UnwrapRef<typeof import('@vueuse/core')['useMouse']>
    readonly useMouseInElement: UnwrapRef<typeof import('@vueuse/core')['useMouseInElement']>
    readonly useMousePressed: UnwrapRef<typeof import('@vueuse/core')['useMousePressed']>
    readonly useMutationObserver: UnwrapRef<typeof import('@vueuse/core')['useMutationObserver']>
    readonly useNavigatorLanguage: UnwrapRef<typeof import('@vueuse/core')['useNavigatorLanguage']>
    readonly useNetwork: UnwrapRef<typeof import('@vueuse/core')['useNetwork']>
    readonly useNow: UnwrapRef<typeof import('@vueuse/core')['useNow']>
    readonly useNuxtApp: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['useNuxtApp']>
    readonly useNuxtData: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/asyncData')['useNuxtData']>
    readonly useNuxtDevTools: UnwrapRef<typeof import('../../node_modules/@nuxt/devtools/dist/runtime/use-nuxt-devtools')['useNuxtDevTools']>
    readonly useObjectUrl: UnwrapRef<typeof import('@vueuse/core')['useObjectUrl']>
    readonly useOffsetPagination: UnwrapRef<typeof import('@vueuse/core')['useOffsetPagination']>
    readonly useOnline: UnwrapRef<typeof import('@vueuse/core')['useOnline']>
    readonly useOrder: UnwrapRef<typeof import('../../composables/useOrder')['useOrder']>
    readonly usePageLeave: UnwrapRef<typeof import('@vueuse/core')['usePageLeave']>
    readonly useParallax: UnwrapRef<typeof import('@vueuse/core')['useParallax']>
    readonly useParentElement: UnwrapRef<typeof import('@vueuse/core')['useParentElement']>
    readonly usePaymentProviders: UnwrapRef<typeof import('../../composables/usePaymentProviders')['usePaymentProviders']>
    readonly usePerformanceObserver: UnwrapRef<typeof import('@vueuse/core')['usePerformanceObserver']>
    readonly usePermission: UnwrapRef<typeof import('@vueuse/core')['usePermission']>
    readonly usePinia: UnwrapRef<typeof import('../../node_modules/@pinia/nuxt/dist/runtime/composables')['usePinia']>
    readonly usePointer: UnwrapRef<typeof import('@vueuse/core')['usePointer']>
    readonly usePointerLock: UnwrapRef<typeof import('@vueuse/core')['usePointerLock']>
    readonly usePointerSwipe: UnwrapRef<typeof import('@vueuse/core')['usePointerSwipe']>
    readonly usePopper: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/usePopper')['usePopper']>
    readonly usePrecision: UnwrapRef<typeof import('../../node_modules/@vueuse/math')['usePrecision']>
    readonly usePreferredColorScheme: UnwrapRef<typeof import('@vueuse/core')['usePreferredColorScheme']>
    readonly usePreferredContrast: UnwrapRef<typeof import('@vueuse/core')['usePreferredContrast']>
    readonly usePreferredDark: UnwrapRef<typeof import('@vueuse/core')['usePreferredDark']>
    readonly usePreferredLanguages: UnwrapRef<typeof import('@vueuse/core')['usePreferredLanguages']>
    readonly usePreferredReducedMotion: UnwrapRef<typeof import('@vueuse/core')['usePreferredReducedMotion']>
    readonly usePreferredReducedTransparency: UnwrapRef<typeof import('@vueuse/core')['usePreferredReducedTransparency']>
    readonly usePreviewMode: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/preview')['usePreviewMode']>
    readonly usePrevious: UnwrapRef<typeof import('@vueuse/core')['usePrevious']>
    readonly useProduct: UnwrapRef<typeof import('../../composables/useProduct')['useProduct']>
    readonly useProductDescription: UnwrapRef<typeof import('../../composables/useProductDescription')['useProductDescription']>
    readonly useProductReviews: UnwrapRef<typeof import('../../composables/useProductReviews')['useProductReviews']>
    readonly useProjection: UnwrapRef<typeof import('../../node_modules/@vueuse/math')['useProjection']>
    readonly useProvideButtonGroup: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useButtonGroup')['useProvideButtonGroup']>
    readonly useRafFn: UnwrapRef<typeof import('@vueuse/core')['useRafFn']>
    readonly useRecommendations: UnwrapRef<typeof import('../../composables/useRecommendations')['useRecommendations']>
    readonly useRefHistory: UnwrapRef<typeof import('@vueuse/core')['useRefHistory']>
    readonly useRequestEvent: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestEvent']>
    readonly useRequestFetch: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestFetch']>
    readonly useRequestHeader: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestHeader']>
    readonly useRequestHeaders: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useRequestHeaders']>
    readonly useRequestURL: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/url')['useRequestURL']>
    readonly useResizeObserver: UnwrapRef<typeof import('@vueuse/core')['useResizeObserver']>
    readonly useResponseHeader: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/ssr')['useResponseHeader']>
    readonly useRound: UnwrapRef<typeof import('../../node_modules/@vueuse/math')['useRound']>
    readonly useRoute: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['useRoute']>
    readonly useRouteAnnouncer: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/route-announcer')['useRouteAnnouncer']>
    readonly useRouteBaseName: UnwrapRef<typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useRouteBaseName']>
    readonly useRouter: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/router')['useRouter']>
    readonly useRuntimeConfig: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/nuxt')['useRuntimeConfig']>
    readonly useRuntimeHook: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/runtime-hook')['useRuntimeHook']>
    readonly useSSRWidth: UnwrapRef<typeof import('@vueuse/core')['useSSRWidth']>
    readonly useScreenOrientation: UnwrapRef<typeof import('@vueuse/core')['useScreenOrientation']>
    readonly useScreenSafeArea: UnwrapRef<typeof import('@vueuse/core')['useScreenSafeArea']>
    readonly useScript: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScript']>
    readonly useScriptClarity: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptClarity']>
    readonly useScriptCloudflareWebAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCloudflareWebAnalytics']>
    readonly useScriptCrisp: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptCrisp']>
    readonly useScriptEventPage: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptEventPage']>
    readonly useScriptFathomAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptFathomAnalytics']>
    readonly useScriptGoogleAdsense: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAdsense']>
    readonly useScriptGoogleAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleAnalytics']>
    readonly useScriptGoogleMaps: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleMaps']>
    readonly useScriptGoogleTagManager: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptGoogleTagManager']>
    readonly useScriptHotjar: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptHotjar']>
    readonly useScriptIntercom: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptIntercom']>
    readonly useScriptLemonSqueezy: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptLemonSqueezy']>
    readonly useScriptMatomoAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMatomoAnalytics']>
    readonly useScriptMetaPixel: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptMetaPixel']>
    readonly useScriptNpm: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptNpm']>
    readonly useScriptPlausibleAnalytics: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptPlausibleAnalytics']>
    readonly useScriptSegment: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptSegment']>
    readonly useScriptStripe: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptStripe']>
    readonly useScriptTag: UnwrapRef<typeof import('@vueuse/core')['useScriptTag']>
    readonly useScriptTriggerConsent: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerConsent']>
    readonly useScriptTriggerElement: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptTriggerElement']>
    readonly useScriptVimeoPlayer: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptVimeoPlayer']>
    readonly useScriptXPixel: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptXPixel']>
    readonly useScriptYouTubePlayer: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/script-stubs')['useScriptYouTubePlayer']>
    readonly useScroll: UnwrapRef<typeof import('@vueuse/core')['useScroll']>
    readonly useScrollLock: UnwrapRef<typeof import('@vueuse/core')['useScrollLock']>
    readonly useSeoMeta: UnwrapRef<typeof import('../../node_modules/@unhead/vue')['useSeoMeta']>
    readonly useServerHead: UnwrapRef<typeof import('../../node_modules/@unhead/vue')['useServerHead']>
    readonly useServerHeadSafe: UnwrapRef<typeof import('../../node_modules/@unhead/vue')['useServerHeadSafe']>
    readonly useServerSeoMeta: UnwrapRef<typeof import('../../node_modules/@unhead/vue')['useServerSeoMeta']>
    readonly useSessionStorage: UnwrapRef<typeof import('@vueuse/core')['useSessionStorage']>
    readonly useSetI18nParams: UnwrapRef<typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useSetI18nParams']>
    readonly useShadowRoot: UnwrapRef<typeof import('../../node_modules/vue')['useShadowRoot']>
    readonly useShare: UnwrapRef<typeof import('@vueuse/core')['useShare']>
    readonly useShortcuts: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useShortcuts')['useShortcuts']>
    readonly useSlideover: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useSlideover')['useSlideover']>
    readonly useSlots: UnwrapRef<typeof import('../../node_modules/vue')['useSlots']>
    readonly useSorted: UnwrapRef<typeof import('@vueuse/core')['useSorted']>
    readonly useSpeechRecognition: UnwrapRef<typeof import('@vueuse/core')['useSpeechRecognition']>
    readonly useSpeechSynthesis: UnwrapRef<typeof import('@vueuse/core')['useSpeechSynthesis']>
    readonly useState: UnwrapRef<typeof import('../../node_modules/nuxt/dist/app/composables/state')['useState']>
    readonly useStepper: UnwrapRef<typeof import('@vueuse/core')['useStepper']>
    readonly useStorageAsync: UnwrapRef<typeof import('@vueuse/core')['useStorageAsync']>
    readonly useStripe: UnwrapRef<typeof import('../../composables/useStripe')['useStripe']>
    readonly useStyleTag: UnwrapRef<typeof import('@vueuse/core')['useStyleTag']>
    readonly useSum: UnwrapRef<typeof import('../../node_modules/@vueuse/math')['useSum']>
    readonly useSupported: UnwrapRef<typeof import('@vueuse/core')['useSupported']>
    readonly useSwipe: UnwrapRef<typeof import('@vueuse/core')['useSwipe']>
    readonly useSwitchLocalePath: UnwrapRef<typeof import('../../node_modules/@nuxtjs/i18n/dist/runtime/composables/index')['useSwitchLocalePath']>
    readonly useTemplateRef: UnwrapRef<typeof import('../../node_modules/vue')['useTemplateRef']>
    readonly useTemplateRefsList: UnwrapRef<typeof import('@vueuse/core')['useTemplateRefsList']>
    readonly useTextDirection: UnwrapRef<typeof import('@vueuse/core')['useTextDirection']>
    readonly useTextSelection: UnwrapRef<typeof import('@vueuse/core')['useTextSelection']>
    readonly useTextareaAutosize: UnwrapRef<typeof import('@vueuse/core')['useTextareaAutosize']>
    readonly useThrottle: UnwrapRef<typeof import('@vueuse/core')['useThrottle']>
    readonly useThrottleFn: UnwrapRef<typeof import('@vueuse/core')['useThrottleFn']>
    readonly useThrottledRefHistory: UnwrapRef<typeof import('@vueuse/core')['useThrottledRefHistory']>
    readonly useTimeAgo: UnwrapRef<typeof import('@vueuse/core')['useTimeAgo']>
    readonly useTimeout: UnwrapRef<typeof import('@vueuse/core')['useTimeout']>
    readonly useTimeoutFn: UnwrapRef<typeof import('@vueuse/core')['useTimeoutFn']>
    readonly useTimeoutPoll: UnwrapRef<typeof import('@vueuse/core')['useTimeoutPoll']>
    readonly useTimer: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useTimer')['useTimer']>
    readonly useTimestamp: UnwrapRef<typeof import('@vueuse/core')['useTimestamp']>
    readonly useTitle: UnwrapRef<typeof import('@vueuse/core')['useTitle']>
    readonly useToNumber: UnwrapRef<typeof import('@vueuse/core')['useToNumber']>
    readonly useToString: UnwrapRef<typeof import('@vueuse/core')['useToString']>
    readonly useToast: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useToast')['useToast']>
    readonly useToggle: UnwrapRef<typeof import('@vueuse/core')['useToggle']>
    readonly useTrackEvent: UnwrapRef<typeof import('../../../node_modules/nuxt-gtag/dist/runtime/composables/useTrackEvent')['useTrackEvent']>
    readonly useTransition: UnwrapRef<typeof import('@vueuse/core')['useTransition']>
    readonly useTransitionState: UnwrapRef<typeof import('../../node_modules/vue')['useTransitionState']>
    readonly useTrunc: UnwrapRef<typeof import('../../node_modules/@vueuse/math')['useTrunc']>
    readonly useTrustedShop: UnwrapRef<typeof import('../../composables/useTrustedShop')['useTrustedShop']>
    readonly useUI: UnwrapRef<typeof import('../../node_modules/@nuxt/ui/dist/runtime/composables/useUI')['useUI']>
    readonly useUrlSearchParams: UnwrapRef<typeof import('@vueuse/core')['useUrlSearchParams']>
    readonly useUserMedia: UnwrapRef<typeof import('@vueuse/core')['useUserMedia']>
    readonly useUtilities: UnwrapRef<typeof import('../../composables/useUtilities')['useUtilities']>
    readonly useVModel: UnwrapRef<typeof import('@vueuse/core')['useVModel']>
    readonly useVModels: UnwrapRef<typeof import('@vueuse/core')['useVModels']>
    readonly useVibrate: UnwrapRef<typeof import('@vueuse/core')['useVibrate']>
    readonly useVirtualList: UnwrapRef<typeof import('@vueuse/core')['useVirtualList']>
    readonly useWakeLock: UnwrapRef<typeof import('@vueuse/core')['useWakeLock']>
    readonly useWebNotification: UnwrapRef<typeof import('@vueuse/core')['useWebNotification']>
    readonly useWebSocket: UnwrapRef<typeof import('@vueuse/core')['useWebSocket']>
    readonly useWebWorker: UnwrapRef<typeof import('@vueuse/core')['useWebWorker']>
    readonly useWebWorkerFn: UnwrapRef<typeof import('@vueuse/core')['useWebWorkerFn']>
    readonly useWindowFocus: UnwrapRef<typeof import('@vueuse/core')['useWindowFocus']>
    readonly useWindowScroll: UnwrapRef<typeof import('@vueuse/core')['useWindowScroll']>
    readonly useWindowSize: UnwrapRef<typeof import('@vueuse/core')['useWindowSize']>
    readonly watch: UnwrapRef<typeof import('../../node_modules/vue')['watch']>
    readonly watchArray: UnwrapRef<typeof import('@vueuse/core')['watchArray']>
    readonly watchAtMost: UnwrapRef<typeof import('@vueuse/core')['watchAtMost']>
    readonly watchDebounced: UnwrapRef<typeof import('@vueuse/core')['watchDebounced']>
    readonly watchDeep: UnwrapRef<typeof import('@vueuse/core')['watchDeep']>
    readonly watchEffect: UnwrapRef<typeof import('../../node_modules/vue')['watchEffect']>
    readonly watchIgnorable: UnwrapRef<typeof import('@vueuse/core')['watchIgnorable']>
    readonly watchImmediate: UnwrapRef<typeof import('@vueuse/core')['watchImmediate']>
    readonly watchOnce: UnwrapRef<typeof import('@vueuse/core')['watchOnce']>
    readonly watchPausable: UnwrapRef<typeof import('@vueuse/core')['watchPausable']>
    readonly watchPostEffect: UnwrapRef<typeof import('../../node_modules/vue')['watchPostEffect']>
    readonly watchSyncEffect: UnwrapRef<typeof import('../../node_modules/vue')['watchSyncEffect']>
    readonly watchThrottled: UnwrapRef<typeof import('@vueuse/core')['watchThrottled']>
    readonly watchTriggerable: UnwrapRef<typeof import('@vueuse/core')['watchTriggerable']>
    readonly watchWithFilter: UnwrapRef<typeof import('@vueuse/core')['watchWithFilter']>
    readonly whenever: UnwrapRef<typeof import('@vueuse/core')['whenever']>
    readonly withCtx: UnwrapRef<typeof import('../../node_modules/vue')['withCtx']>
    readonly withDirectives: UnwrapRef<typeof import('../../node_modules/vue')['withDirectives']>
    readonly withKeys: UnwrapRef<typeof import('../../node_modules/vue')['withKeys']>
    readonly withMemo: UnwrapRef<typeof import('../../node_modules/vue')['withMemo']>
    readonly withModifiers: UnwrapRef<typeof import('../../node_modules/vue')['withModifiers']>
    readonly withScopeId: UnwrapRef<typeof import('../../node_modules/vue')['withScopeId']>
  }
}