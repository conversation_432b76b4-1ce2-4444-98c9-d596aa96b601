import { defineEvent<PERSON><PERSON><PERSON>, parseCookies, createError } from 'h3'
import { AUTH_COOKIE_NAME } from '~/constants/auth'
import { useRuntimeConfig } from '#imports'

// Constants (replace with your actual admin credentials)
const ADMIN_EMAIL = '<EMAIL>'
const ADMIN_PASSWORD = 'Business95!'

// Helper to get admin token
async function getAdminToken(medusaUrl: string) {
  try {
    console.log('Obtaining admin token')
    const loginResponse = await fetch(`${medusaUrl}/auth/user/emailpass`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        email: ADMIN_EMAIL,
        password: ADMIN_PASSWORD,
      }),
    })
    
    if (!loginResponse.ok) {
      const error = await loginResponse.json().catch(() => ({}))
      console.error('Admin login failed:', error)
      throw new Error(`Admin login failed: ${error.message || loginResponse.statusText}`)
    }
    
    const loginData = await loginResponse.json()
    
    if (loginData.token) {
      console.log('Admin login successful, received JWT token')
      return loginData.token
    }
    
    throw new Error('Admin login response did not contain a token')
  } catch (error: any) {
    console.error('Error getting admin token:', error.message)
    throw error
  }
}

// Transform Medusa order data to expected frontend format
function transformOrderData(order: any) {
  try {
    if (!order) return null;
    
    // Extract total from summary if present
    const orderTotal = order.summary?.current_order_total || 
                      order.summary?.original_order_total || 
                      0;
    
    // Parse payment and fulfillment status to get a combined status
    let displayStatus = "pending";
    
    if (order.payment_status === "captured") {
      displayStatus = "paid";
    } else if (order.payment_status === "authorized") {
      displayStatus = "authorized";
    }
    
    if (order.fulfillment_status === "fulfilled") {
      displayStatus = "processing";
    } else if (order.fulfillment_status === "shipped") {
      displayStatus = "shipped";
    } else if (order.fulfillment_status === "delivered") {
      displayStatus = "completed";
    }
    
    // Format items data
    const formattedItems = order.items?.map((item: any) => ({
      id: item.id,
      title: item.title || item.subtitle || 'Product',
      unit_price: item.unit_price || 0,
      quantity: item.quantity || 1,
      thumbnail: item.thumbnail || null,
      variant: {
        id: item.variant_id || '',
        title: item.variant_title || '',
        sku: item.variant_sku || '',
        product: {
          title: item.product_title || '',
          thumbnail: item.thumbnail || ''
        }
      }
    })) || [];
    
    // Format the order to match our interface
    return {
      id: order.id,
      display_id: order.display_id?.toString() || '',
      status: displayStatus,
      total: orderTotal,
      subtotal: orderTotal,  // If subtotal is missing, use total
      shipping_total: 0,     // Default to 0 if missing
      tax_total: 0,          // Default to 0 if missing
      items: formattedItems,
      shipping_address: {},  // Default empty object if missing
      billing_address: {},   // Default empty object if missing
      shipping_methods: [],  // Default empty array if missing
      payments: [],          // Default empty array if missing
      created_at: order.created_at,
      updated_at: order.updated_at,
      currency_code: 'RON'   // Default to RON
    };
  } catch (error) {
    console.error("Error transforming order data:", error);
    return null;
  }
}

export default defineEventHandler(async (event) => {
  try {
    // Get order ID from the URL params
    const orderId = event.context.params?.id
    
    if (!orderId) {
      throw createError({
        statusCode: 400,
        message: 'Order ID is required'
      })
    }
    
    console.log('Fetching order with ID:', orderId)
    
    // Extract cookies and check for authentication
    const cookies = parseCookies(event)
    
    // Check for both 'auth' and the defined AUTH_COOKIE_NAME for compatibility
    const authCookie = cookies[AUTH_COOKIE_NAME] || cookies['auth']
    
    if (!authCookie) {
      throw createError({
        statusCode: 401,
        message: 'Authentication required'
      })
    }
    
    const config = useRuntimeConfig()
    const cloudflareUrl = config.public.cloudflareApiUrl || 'http://localhost:8787'

    // Parse auth cookie to get token
    let authData
    try {
      authData = JSON.parse(authCookie)
    } catch (e) {
      throw createError({
        statusCode: 401,
        message: 'Invalid authentication data'
      })
    }

    // Fetch order details from Cloudflare backend
    const orderResponse = await fetch(`${cloudflareUrl}/store/orders/${orderId}`, {
      method: 'GET',
      headers: {
        "Authorization": `Bearer ${authData.accessToken}`,
        "Content-Type": "application/json"
      }
    })

    if (!orderResponse.ok) {
      const errorData = await orderResponse.json().catch(() => ({}))
      console.error('Error fetching order details:', errorData)

      throw createError({
        statusCode: orderResponse.status,
        message: `Failed to fetch order details: ${errorData.message || orderResponse.statusText}`
      })
    }

    const orderData = await orderResponse.json()
    console.log('Order details retrieved successfully')

    // Return order data (assuming Cloudflare backend returns properly formatted data)
    const order = orderData.success ? orderData.data : orderData.order
    
    // Return order data
    return {
      order: order
    }
  } catch (error: any) {
    console.error('Error in order details endpoint:', error.message)
    throw createError({
      statusCode: error.statusCode || 500,
      message: error.message || 'An unknown error occurred'
    })
  }
}) 